version: '3.7'
services:
  node-exporter:
    privileged: true
    image: prom/node-exporter
    volumes:
      - '/proc:/host/proc:ro'
      - '/sys:/host/sys:ro'
      - '/:/rootfs:ro'
      - '/volume1:/volume1:ro'
      - '/volume2:/volume2:ro'
    ports:
      - '9100:9100'
    restart: always
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points'
      - "^/(sys|proc|dev|host|etc|tmp|cgroup|cgmfs|shm|rootfs/var/lib/docker/containers|rootfs/var/lib/docker/overlay2|rootfs/run/docker/netns|rootfs/var/lib/docker/aufs)($$|/)"
      - '--collector.filesystem.ignored-fs-types'
      - "^/(autofs|binfmt_misc|cgroup|configfs|debugfs|devpts|devtmpfs|fusectl|hugetlbfs|mqueue|overlay|proc|procfs|pstore|rpc_pipefs|securityfs|sysfs|tracefs)($$|/)"
      - '--collector.mountstats'
