{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": false, "gnetId": 5345, "graphTooltip": 0, "id": 12, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 15, "panels": [], "repeat": "targets", "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "title": "$targets UP/DOWN Status", "type": "row"}, {"cacheTimeout": null, "colorBackground": true, "colorValue": false, "colors": ["#d44a3a", "rgba(237, 129, 40, 0.89)", "#299c46"], "datasource": "prometheus", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 24, "x": 0, "y": 1}, "id": 2, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "minSpan": 3, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": null, "repeatDirection": "h", "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "probe_success{instance=~\"$targets\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "thresholds": "1,1", "title": "$targets", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}, {"op": "=", "text": "UP", "value": "1"}, {"op": "=", "text": "DOWN", "value": "0"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": true, "colorValue": false, "colors": ["#d44a3a", "rgba(237, 129, 40, 0.89)", "#299c46"], "datasource": "prometheus", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 6, "x": 0, "y": 3}, "id": 18, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "minSpan": 3, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "h", "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "probe_http_ssl{instance=~\"$targets\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "thresholds": "0,1", "title": "SSL", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}, {"op": "=", "text": "YES", "value": "1"}, {"op": "=", "text": "NO", "value": "0"}], "valueName": "current"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "fill": 1, "gridPos": {"h": 6, "w": 9, "x": 6, "y": 3}, "id": 17, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "probe_duration_seconds{instance=~\"$targets\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "seconds", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Probe Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "fill": 1, "gridPos": {"h": 6, "w": 9, "x": 15, "y": 3}, "id": 21, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "probe_dns_lookup_time_seconds{instance=~\"$targets\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "seconds", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "DNS Lookup", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "colorBackground": true, "colorValue": false, "colors": ["#d44a3a", "rgba(237, 129, 40, 0.89)", "#299c46"], "datasource": "prometheus", "decimals": null, "format": "short", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 6, "x": 0, "y": 5}, "id": 19, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "minSpan": 3, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "h", "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "(probe_ssl_earliest_cert_expiry{instance=\"$targets\"}-time())/(86400*30)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "thresholds": "6,2", "title": "SSL Cert Expiration - months left", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "prometheus", "decimals": 0, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 6, "x": 0, "y": 7}, "id": 20, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "minSpan": 3, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeatDirection": "h", "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "probe_http_status_code{instance=~\"$targets\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "thresholds": "200,299,300", "title": "HTTP Status Code", "transparent": false, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}, {"op": "=", "text": "YES", "value": "1"}, {"op": "=", "text": "NO", "value": "0"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "prometheus", "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 12, "x": 0, "y": 9}, "id": 23, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "avg(probe_duration_seconds{instance=~\"$targets\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "thresholds": "", "title": "Average Probe Duration", "type": "singlestat", "valueFontSize": "50%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "prometheus", "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 12, "x": 12, "y": 9}, "id": 24, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "avg(probe_dns_lookup_time_seconds{instance=~\"$targets\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "thresholds": "", "title": "Average DNS Lookup", "type": "singlestat", "valueFontSize": "50%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "fill": 1, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 11}, "id": 26, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "probe_success{instance=~\"$targets\"}", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "lt", "value": 0, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node history", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 18}, "id": 27, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg_over_time(probe_duration_seconds{instance=~\"$targets\"}[3m])", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "avg probe duration (5 minutes)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 18}, "id": 28, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scopedVars": {"targets": {"selected": true, "text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(probe_duration_seconds{instance=~\"$targets\"}[3m])", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "rate probe duration (5 minutes)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "1m", "schemaVersion": 16, "style": "dark", "tags": ["blackbox", "prometheus"], "templating": {"list": [{"auto": true, "auto_count": 10, "auto_min": "10s", "current": {"text": "auto", "value": "$__auto_interval_interval"}, "hide": 0, "label": "Interval", "name": "interval", "options": [{"selected": true, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": false, "text": "5s", "value": "5s"}, {"selected": false, "text": "10s", "value": "10s"}, {"selected": false, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "5s,10s,30s,1m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "refresh": 2, "skipUrlSync": false, "type": "interval"}, {"allValue": null, "current": {"text": "https://bamboo.spcph.local", "value": "https://bamboo.spcph.local"}, "datasource": "prometheus", "definition": "", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "targets", "options": [], "query": "label_values(probe_success, instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Blackbox Exporter Overview", "uid": "xtkCtBkiz", "version": 2}