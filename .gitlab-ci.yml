variables:
# non technical
  V_DEPLOY_HOST: prometheus.scalepoint.tech
  V_DEPLOY_USER: root
  V_FOLDER: prometheus
stages:
  - sanity_check
  - deploy
promtool_check:
  stage: sanity_check
  image:
    name: prom/prometheus:latest
    entrypoint: [""]
  script:
    - cd prometheus
    - promtool check config prometheus.yml
    - promtool check rules *.rules
  tags:
    - docker
    - gitops
  only:
    refs:
      - web
      - webhook
      - master
amtool_check:
  stage: sanity_check
  image:
    name: prom/alertmanager:latest
    entrypoint: [""]
  script:
    - cd prometheus
    - amtool check-config alertmanager.yml
  tags:
    - docker
    - gitops
  only:
    refs:
      - web
      - webhook
      - master
docker_ssh_deploy:
  stage: deploy
  retry: 2
  image: $PIPELINE_IMAGE:$PIPELINE_IMAGE_TAG
  services:
    - docker:$DOCKER_DIND
  before_script:
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY_PROM" | tr -d '\r' > ~/.ssh/id_rsa; chmod -R 700 ~/.ssh; ssh-add ~/.ssh/id_rsa
  script:
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "mkdir -p /srv/$V_FOLDER||true"
    - rsync -Pazv -r --exclude-from=.exclude.lst --delete-after --delete-excluded ./ "$V_DEPLOY_USER"@"$V_DEPLOY_HOST":/srv/"$V_FOLDER"
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "cd /srv/$V_FOLDER &&  /usr/bin/docker-compose pull"
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "cd /srv && if cd $V_FOLDER;then if [ -f docker-compose.yaml ];then /usr/bin/docker compose down --remove-orphans --timeout 120||true;fi;fi && exit"
    # fixes for different volume behaviour
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "chmod 777 -R /var/lib/docker/volumes/prometheus_consul_data"
    # replace dev data with prod data
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "cd /srv/$V_FOLDER; cp -pf ./traefik/etc/cloudflare.env.prod ./traefik/etc/cloudflare.env"
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "cd /srv/$V_FOLDER; cp -pf ./prometheus/alertmanager-prod.yml ./prometheus/alertmanager.yml"
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "cd /srv/$V_FOLDER; cp -pf ./prom2teams/config.prod ./prom2teams/config.ini"
    # secrets
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "cd /srv/$V_FOLDER; sed -i 's|^AZURE_CLIENT_ID=.*|AZURE_CLIENT_ID=$AZURE_CLIENT_ID|g' .env"
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "cd /srv/$V_FOLDER; sed -i 's|^AZURE_CLIENT_SECRET=.*|AZURE_CLIENT_SECRET=$AZURE_CLIENT_SECRET|g' .env"
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "cd /srv/$V_FOLDER; sed -i 's|^AZURE_TENANT_ID=.*|AZURE_TENANT_ID=$AZURE_TENANT_ID|g' .env"
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "cd /srv/$V_FOLDER; sed -i 's|PLACEHOLDER_GF_AUTH_AZUREAD_CLIENT_SECRET|$GF_AUTH_AZUREAD_CLIENT_SECRET|g' docker-compose.yaml"
    # deploy
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "sudo restorecon -vR /srv/"
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "cd /srv/$V_FOLDER &&  /usr/bin/docker compose up -d"
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "cd /srv/$V_FOLDER &&  /usr/bin/docker compose ps"
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "/usr/bin/docker image prune --all -f"
  after_script:
    - sleep 60
    # send test alert
    - ssh "$V_DEPLOY_USER"@$V_DEPLOY_HOST "/srv/$V_FOLDER/prometheus/testalerts.sh"
  tags:
    - docker
    - gitops
  only:
    refs:
      - web
      - webhook
      - master
    changes:
      - "*"
      - "**/*"
