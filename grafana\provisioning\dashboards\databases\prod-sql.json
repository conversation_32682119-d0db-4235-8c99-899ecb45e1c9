{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "PRO-SQL Basic info", "editable": true, "gnetId": null, "graphTooltip": 0, "links": [], "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxDataPoints": "", "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "functions": [{"$$hashKey": "object:155", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan: CPU Usage(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.lan: CPU Usage(.*)/, $1)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/^prod-sql.*/"}, "item": {"filter": "CPU utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:75", "decimals": null, "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:76", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 8}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxDataPoints": "", "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "functions": [{"$$hashKey": "object:153", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan: CPU Usage(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.lan: CPU Usage(.*)/, $1)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/^prod-sql.cl0(1|2).*/"}, "item": {"filter": "CPU utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage ECA", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:187", "decimals": null, "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:188", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 8}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxDataPoints": "", "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "functions": [{"$$hashKey": "object:233", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan: CPU Usage(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.lan: CPU Usage(.*)/, $1)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/^prod-sql.cl0(3|4).*/"}, "item": {"filter": "CPU utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage ECB", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:267", "decimals": null, "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:268", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 8}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxDataPoints": "", "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "functions": [{"$$hashKey": "object:313", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan: CPU Usage(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.lan: CPU Usage(.*)/, $1)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/^prod-sql.cl0(5|6).*/"}, "item": {"filter": "CPU utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage ECC", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:347", "decimals": null, "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:348", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 15}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxDataPoints": "", "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "functions": [{"$$hashKey": "object:393", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan: CPU Usage(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.lan: CPU Usage(.*)/, $1)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/^prod-sql.cl0(7|8).*/"}, "item": {"filter": "CPU utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage SHARED", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:427", "decimals": null, "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:428", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 15}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxDataPoints": "", "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "functions": [{"$$hashKey": "object:473", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan: CPU Usage(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.lan: CPU Usage(.*)/, $1)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/^prod-sql.cl(09|10).*/"}, "item": {"filter": "CPU utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage SHOP", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:507", "decimals": null, "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:508", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 15}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxDataPoints": "", "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "functions": [{"$$hashKey": "object:553", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan: CPU Usage(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.lan: CPU Usage(.*)/, $1)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/^prod-sql.cl(11|12).*/"}, "item": {"filter": "CPU utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage Internal", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:587", "decimals": null, "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:588", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 22}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxDataPoints": "", "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface Microsoft Hyper-V Network Adapter(Ethernet)"}, "functions": [{"$$hashKey": "object:709", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.lan: Interface Microsoft Hyper-V Network Adapter(Ethernet): Bits received", ":"], "text": "replaceAlias(.scalepoint.lan: Interface Microsoft Hyper-V Network Adapter(Ethernet): Bits received, :)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/^prod-sql.*/"}, "item": {"filter": "Interface Microsoft Hyper-V Network Adapter(Ethernet): Bits received"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "item": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Interface Microsoft Hyper-V Network Adapter(Ethernet): Bits received", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:667", "decimals": null, "format": "bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:668", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 30}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxDataPoints": "", "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface Microsoft Hyper-V Network Adapter(Ethernet)"}, "functions": [{"$$hashKey": "object:808", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.lan: Interface Microsoft Hyper-V Network Adapter(Ethernet): Bits sent", ":"], "text": "replaceAlias(.scalepoint.lan: Interface Microsoft Hyper-V Network Adapter(Ethernet): Bits sent, :)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/^prod-sql.*/"}, "item": {"filter": "Interface Microsoft Hyper-V Network Adapter(Ethernet): Bits sent"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "item": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Interface Microsoft Hyper-V Network Adapter(Ethernet): Bits sent", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transparent": true, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:842", "decimals": null, "format": "bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:843", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "zabbix", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 13, "x": 0, "y": 38}, "id": 12, "options": {"displayMode": "gradient", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {"valueSize": 13}}, "pluginVersion": "7.5.3", "repeat": null, "targets": [{"application": {"filter": "Status"}, "functions": [{"def": {"name": "top", "category": "Filter", "params": [{"name": "number", "type": "int"}, {"name": "value", "type": "string", "options": ["avg", "min", "max", "sum", "count", "median"]}], "defaultParams": [5, "avg"]}, "params": ["20", "avg"], "text": "top(20, avg)", "added": false, "$$hashKey": "object:102"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.lan: Uptime", "."], "text": "replaceAlias(.scalepoint.lan: Uptime, .)", "$$hashKey": "object:99"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/^prod-sql.*/"}, "item": {"filter": "Uptime"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "timeFrom": null, "timeShift": null, "title": "Uptime", "type": "bargauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 11, "x": 13, "y": 38}, "hiddenSeries": false, "id": 13, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Filesystem C:"}, "functions": [{"$$hashKey": "object:620", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.lan: C:: Space utilization", "."], "text": "replaceAlias(.scalepoint.lan: C:: Space utilization, .)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/^prod-sql.*/"}, "item": {"filter": "C:: Space utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "C: - Space utilization", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:658", "format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:659", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 27, "style": "dark", "tags": ["sql", "production", "#ask_slk"], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "PROD-SQL"}