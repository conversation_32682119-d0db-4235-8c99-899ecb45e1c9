# prometheus/alert-azure.rules
groups:
- name: azure-certificates
  rules:
  - alert: AzureCertificateIssues
    expr: azure_ad_certificate_expiry_days <= 30
    for: 5m
    labels:
      severity: monitoring-only
      team: monitoring
    annotations:
      summary: '{{ if eq $value 0.0 }}Azure certificate EXPIRED!{{ else }}Azure certificate expires in {{ $value }} days{{ end }}'
      description: 'Certificate for app {{ $labels.app_name }} ({{ $labels.cert_id }}) {{ if eq $value 0.0 }}has EXPIRED!{{ else }}expires in {{ $value }} days{{ end }}'

  - alert: AzureSecretIssues  
    expr: azure_ad_secret_expiry_days <= 30
    for: 5m
    labels:
      severity: monitoring-only
      team: monitoring
    annotations:
      summary: '{{ if eq $value 0.0 }}Azure secret EXPIRED!{{ else }}Azure secret expires in {{ $value }} days{{ end }}'
      description: 'Secret for app {{ $labels.app_name }} ({{ $labels.secret_id }}) {{ if eq $value 0.0 }}has EXPIRED!{{ else }}expires in {{ $value }} days{{ end }}'