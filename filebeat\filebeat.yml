# traefik logs:
filebeat.inputs:
  - type: filestream
    id: traefik
    take_over: true
    enabled: true
    parsers:
      - ndjson:
          expand_keys: true
          add_error_key: true
          keys_under_root: true
    close.on_state_change.inactive: '1h'
    ignore_inactive: 'since_last_start'
    clean_inactive: '5h'
    paths:
      - /var/log/traefik/access.log
    fields_under_root: true
    fields:
      applicationName: traefik
      id: traefik
      traefik_version: '${TRAEFIK_TAG}'
      serviceName: '${SERVICE_NAME}'
processors:
- add_cloud_metadata: ~
- rename:
    fields:
     - from: "source"
       to: "file"
    ignore_missing: true
    fail_on_error: true
name: ${HOST_NAME}
tags: ["traefik","prometheus"]

# logstash
output.logstash:
  enabled: true
  hosts: '${LOGSTASH_HOST}'
  timeout: 240
  bulk_max_size: 128
  ssl:
    enabled: true
    verification_mode: none
    supported_protocols: TLSv1.2
logging:
  level: info
  to_files: false
  to_stderr: true
