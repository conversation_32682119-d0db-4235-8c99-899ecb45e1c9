#!/bin/bash
# ssh <EMAIL> "cd /srv/prometheus"
# scp -r ./prometheus/alert.rules <EMAIL>:/srv/prometheus/
rsync -avhv --progress --no-owner --no-perms --no-group ./prometheus/blackbox/* <EMAIL>:/srv/prometheus/blackbox
rsync -avhv --progress --no-owner --no-perms --no-group ./prometheus/prometheus/*.yml <EMAIL>:/srv/prometheus/prometheus
ssh <EMAIL> "cd /srv/prometheus&& docker-compose restart alertmanager blackbox_exporter prometheus"
