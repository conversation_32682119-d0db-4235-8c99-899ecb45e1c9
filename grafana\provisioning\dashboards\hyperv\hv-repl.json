{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Filesystem C:"}, "datasource": "zabbix", "functions": [{"$$hashKey": "object:78", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: C:: Space utilization(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: C:: Space utilization(.*)/, $1)"}], "group": {"filter": "Hyper-V-REPL"}, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "C:: Space utilization"}, "itemTag": {"filter": "Application: Filesystem C:"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "C:: Space utilization", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "series", "show": true, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:247", "format": "percent", "label": "ddsa", "logBase": 1, "show": true}, {"$$hashKey": "object:248", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Filesystem D:"}, "datasource": "zabbix", "functions": [{"$$hashKey": "object:110", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:111", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.tech: D:: Space utilization", ":"], "text": "replaceAlias(.scalepoint.tech: D:: Space utilization, :)"}], "group": {"filter": "Hyper-V-REPL"}, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "D:: Space utilization"}, "itemTag": {"filter": "Application: Filesystem D:"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "D:: Space utilization", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "series", "show": true, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:109", "format": "percent", "logBase": 1, "max": "100", "show": true}, {"$$hashKey": "object:110", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 5, "x": 0, "y": 8}, "hiddenSeries": false, "id": 13, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "HyperV Host"}, "datasource": "zabbix", "functions": [{"$$hashKey": "object:517", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: HyperV VMs Critical(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: HyperV VMs Critical(.*)/, $1)"}, {"$$hashKey": "object:518", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}], "group": {"filter": "Hyper-V-REPL"}, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "HyperV VMs Critical"}, "itemTag": {"filter": "Application: HyperV Host"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "HyperV VMs Critical", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "zabbix", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 9, "w": 7, "x": 5, "y": 8}, "id": 15, "links": [], "options": {"displayMode": "gradient", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}}, "pluginVersion": "8.3.4", "targets": [{"application": {"filter": "Status"}, "datasource": "zabbix", "functions": [{"$$hashKey": "object:396", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:397", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: Uptime(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: Uptime(.*)/, $1)"}], "group": {"filter": "Hyper-V-REPL"}, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "Uptime"}, "itemTag": {"filter": "Application: Status"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "System uptime", "type": "bargauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 8}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Memory"}, "datasource": "zabbix", "functions": [{"$$hashKey": "object:252", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:253", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: Memory utilization(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: Memory utilization(.*)/, $1)"}], "group": {"filter": "Hyper-V-REPL"}, "hide": false, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "Memory utilization"}, "itemTag": {"filter": "Application: Memory"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Memory utilization", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:359", "format": "percent", "logBase": 1, "show": true}, {"$$hashKey": "object:360", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 17}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface Intel(R) Ethernet Converged Network Adapter X540-T2(Ethernet 2)"}, "datasource": "zabbix", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["scalepoint.tech: Interface Intel(R) Ethernet Converged Network Adapter X540-T2(Ethernet 2)", "-"], "text": "replaceAlias(scalepoint.tech: Interface Intel(R) Ethernet Converged Network Adapter X540-T2(Ethernet 2), -)", "$$hashKey": "object:442"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "min"], "text": "top(30, min)", "$$hashKey": "object:443"}], "group": {"filter": "Hyper-V-REPL"}, "hide": false, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "/.*/"}, "itemTag": {"filter": "Application: Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface Intel(R) Ethernet Converged Network Adapter X540-T2(Ethernet 2)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:741", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:742", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"unit": "bps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface Intel(R) I350 Gigabit Network Connection #2(Ethernet 3)"}, "datasource": "zabbix", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["scalepoint.tech: Interface Intel(R) I350 Gigabit Network Connection #2(Ethernet 3)", "-"], "text": "replaceAlias(scalepoint.tech: Interface Intel(R) I350 Gigabit Network Connection #2(Ethernet 3), -)", "$$hashKey": "object:183"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)", "$$hashKey": "object:184"}], "group": {"filter": "Hyper-V-REPL"}, "hide": false, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "/.*/"}, "itemTag": {"filter": "Application: Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface Intel(R) I350 Gigabit Network Connection #2(Ethernet 3)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:529", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:530", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 25}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface Intel(R) I350 Gigabit Network Connection #3(Ethernet 4)"}, "datasource": "zabbix", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["scalepoint.tech: Interface Intel(R) I350 Gigabit Network Connection #3(Ethernet 4):", "-"], "text": "replaceAlias(scalepoint.tech: Interface Intel(R) I350 Gigabit Network Connection #3(Ethernet 4):, -)", "$$hashKey": "object:384"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "min"], "text": "top(30, min)", "$$hashKey": "object:385"}], "group": {"filter": "Hyper-V-REPL"}, "hide": false, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "/.*/"}, "itemTag": {"filter": "Application: Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface Intel(R) I350 Gigabit Network Connection #3 Ethernet 4)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:741", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:742", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "hiddenSeries": false, "id": 18, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface Intel(R) I350 Gigabit Network Connection #4(Ethernet 6)"}, "datasource": "zabbix", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["scalepoint.tech: Interface Intel(R) I350 Gigabit Network Connection #4(Ethernet 6)", "-"], "text": "replaceAlias(scalepoint.tech: Interface Intel(R) I350 Gigabit Network Connection #4(Ethernet 6), -)", "$$hashKey": "object:508"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "min"], "text": "top(30, min)", "$$hashKey": "object:509"}], "group": {"filter": "Hyper-V-REPL"}, "hide": false, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "/.*/"}, "itemTag": {"filter": "Application: Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface Intel(R) I350 Gigabit Network Connection #4(Ethernet 6", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:741", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:742", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 33}, "hiddenSeries": false, "id": 19, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface Intel(R) I350 Gigabit Network Connection(Ethernet)"}, "datasource": "zabbix", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.tech: Interface Intel(R) I350 Gigabit Network Connection(Ethernet):", "-"], "text": "replaceAlias(.scalepoint.tech: Interface Intel(R) I350 Gigabit Network Connection(Ethernet):, -)", "$$hashKey": "object:570"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "min"], "text": "top(30, min)", "$$hashKey": "object:571"}], "group": {"filter": "Hyper-V-REPL"}, "hide": false, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "/.*/"}, "itemTag": {"filter": "Application: Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface Intel(R) I350 Gigabit Network Connection(Ethernet)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:741", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:742", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 41}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface Microsoft Network Adapter Multiplexor Driver(VM-TEAM - VLAN 3)"}, "datasource": "zabbix", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["scalepoint.tech: Interface Microsoft Network Adapter Multiplexor Driver(VM-TEAM - VLAN 3)", "-"], "text": "replaceAlias(scalepoint.tech: Interface Microsoft Network Adapter Multiplexor Driver(VM-TEAM - VLAN 3), -)", "$$hashKey": "object:632"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "min"], "text": "top(30, min)", "$$hashKey": "object:633"}], "group": {"filter": "Hyper-V-REPL"}, "hide": false, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "/.*/"}, "itemTag": {"filter": "Application: Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface Microsoft Network Adapter Multiplexor Driver(VM-TEAM - VLAN 3)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:741", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:742", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 41}, "hiddenSeries": false, "id": 21, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface Intel(R) Ethernet 10G 4P X710 SFP+ rNDC(NIC1)"}, "datasource": "zabbix", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["scalepoint.tech: Interface Intel(R) Ethernet 10G 4P X710 SFP+ rNDC(NIC1)", "-"], "text": "replaceAlias(scalepoint.tech: Interface Intel(R) Ethernet 10G 4P X710 SFP+ rNDC(NIC1), -)", "$$hashKey": "object:694"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "min"], "text": "top(30, min)", "$$hashKey": "object:695"}], "group": {"filter": "Hyper-V-REPL"}, "hide": false, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "/.*/"}, "itemTag": {"filter": "Application: Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface Intel(R) Ethernet 10G 4P X710 SFP+ rNDC(NIC1)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:741", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:742", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 49}, "hiddenSeries": false, "id": 22, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface Intel(R) Ethernet 10G X710 rNDC(NIC4)"}, "datasource": "zabbix", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.tech: Interface Intel(R) Ethernet 10G X710 rNDC(NIC4):", "-"], "text": "replaceAlias(.scalepoint.tech: Interface Intel(R) Ethernet 10G X710 rNDC(NIC4):, -)", "$$hashKey": "object:760"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "min"], "text": "top(30, min)", "$$hashKey": "object:761"}], "group": {"filter": "Hyper-V-REPL"}, "hide": false, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "/.*/"}, "itemTag": {"filter": "Application: Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface Intel(R) Ethernet 10G X710 rNDC(NIC4):", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:741", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:742", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 58}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface QLogic FastLinQ QL41262-DE 25GbE Adapter (VBD Client)(Team1a)"}, "datasource": "zabbix", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["scalepoint.tech: Interface QLogic FastLinQ QL41262-DE 25GbE Adapter (VBD Client)(Team1a)", "-"], "text": "replaceAlias(scalepoint.tech: Interface QLogic FastLinQ QL41262-DE 25GbE Adapter (VBD Client)(Team1a), -)", "$$hashKey": "object:818"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "min"], "text": "top(30, min)", "$$hashKey": "object:819"}], "group": {"filter": "Hyper-V-REPL"}, "hide": false, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "/.*/"}, "itemTag": {"filter": "Application: Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface QLogic FastLinQ QL41262-DE 25GbE Adapter (VBD Client)(Team1a)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:741", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:742", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 67}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface Remote NDIS Compatible Device(Ethernet)"}, "datasource": "zabbix", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.tech: Interface Remote NDIS Compatible Device(Ethernet):", "-"], "text": "replaceAlias(.scalepoint.tech: Interface Remote NDIS Compatible Device(Ethernet):, -)", "$$hashKey": "object:942"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "min"], "text": "top(30, min)", "$$hashKey": "object:943"}], "group": {"filter": "Hyper-V-REPL"}, "hide": false, "host": {"filter": "/.*hpv-repl/"}, "item": {"filter": "/.*/"}, "itemTag": {"filter": "Application: Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface Remote NDIS Compatible Device(Ethernet):", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:741", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:742", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "", "schemaVersion": 34, "style": "dark", "tags": ["hosts", "zabbix", "Repl"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "HPV-REPL"}