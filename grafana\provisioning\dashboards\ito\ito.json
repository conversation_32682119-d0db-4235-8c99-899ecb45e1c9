{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 69, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "HyperV Host"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:223", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["10", "avg"], "text": "top(10, avg)"}, {"$$hashKey": "object:224", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: HyperV Logical CPU Total Runtime %(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: HyperV Logical CPU Total Runtime %(.*)/, $1)"}], "group": {"filter": "Sentia-cluster"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "HyperV Logical CPU Total Runtime %"}, "itemTag": {"filter": "Application: HyperV Host"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "warning", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}, {"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 95}], "timeRegions": [], "title": "CPU Usage Prod-Host", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 9}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "HyperV Host"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:281", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["10", "avg"], "text": "top(10, avg)"}, {"$$hashKey": "object:282", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan: CPU utilization(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.lan: CPU utilization(.*)/, $1)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/.*audit-prod0/"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "warning", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}, {"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 95}], "timeRegions": [], "title": "CPU AUDIT", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 18}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "HyperV Host"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:109", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan: CPU utilization(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.lan: CPU utilization(.*)/, $1)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/.*prod-ecb-wfe/"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "warning", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}, {"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 95}], "timeRegions": [], "title": "CPU CWA", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "CPU HOST,AUDIT,CWA.", "uid": "s3D031KGz", "version": 3, "weekStart": ""}