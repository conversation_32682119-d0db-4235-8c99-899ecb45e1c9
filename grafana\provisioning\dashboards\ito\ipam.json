{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 110, "links": [], "panels": [{"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": [], "unit": "short"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["vm name (count)", "Windows"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 10, "w": 8, "x": 0, "y": 0}, "id": 1, "options": {"displayLabels": ["name", "percent"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["percent", "value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "platform.display", "text": "Platform", "type": "string"}, {"selector": "name", "text": "vm name", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "Operating Systems Distribution", "transformations": [{"id": "groupBy", "options": {"fields": {"Platform": {"aggregations": ["count"], "operation": "groupby"}, "Status": {"aggregations": []}, "vm name": {"aggregations": ["count"], "operation": "aggregate"}}}}, {"id": "filterByValue", "options": {"filters": [{"config": {"id": "equal", "options": {"value": "Decommissioning"}}, "fieldName": "Status"}], "match": "any", "type": "exclude"}}], "type": "piechart"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": [], "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 7, "x": 8, "y": 0}, "id": 12, "options": {"displayLabels": ["percent", "name"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "custom_fields.patch_state", "text": "Patch State", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "Patch State", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "notEqual", "options": {"value": "Decommissioning"}}, "fieldName": "Status"}], "match": "all", "type": "include"}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Patch State": "Patch State"}}}, {"id": "groupBy", "options": {"fields": {"Patch State": {"aggregations": [], "operation": "groupby"}, "Status": {"aggregations": []}, "VM Name": {"aggregations": ["count"], "operation": "aggregate"}}}}], "type": "piechart"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 15, "y": 0}, "id": 2, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "status.value", "text": "Status", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "Decommission Servers", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "equal", "options": {"value": "decommissioning"}}, "fieldName": "Status"}], "match": "any", "type": "include"}}, {"id": "reduce", "options": {"reducers": ["count"]}}], "type": "stat"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "Total VMs", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "notEqual", "options": {"value": "Decommissioning"}}, "fieldName": "Status"}], "match": "all", "type": "include"}}, {"id": "reduce", "options": {"reducers": ["count"]}}], "type": "stat"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "orange", "value": 1}, {"color": "red", "value": 10}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 15, "y": 5}, "id": 3, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "custom_fields.last_update", "text": "Last Update", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "Patch Date > 60 Days", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "regex", "options": {"value": "^(202[0-4])-.*|^2025-0[1-3]-.*"}}, "fieldName": "Last Update"}, {"config": {"id": "notEqual", "options": {"value": "Decommissioning"}}, "fieldName": "Status"}], "match": "all", "type": "include"}}, {"id": "reduce", "options": {"reducers": ["count"]}}, {"id": "filterByValue", "options": {"filters": [{"config": {"id": "equal", "options": {"value": "decommissioning"}}, "fieldName": "Count"}], "match": "all", "type": "exclude"}}], "type": "stat"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 2, "x": 18, "y": 5}, "id": 13, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}, {"selector": "platform.display", "text": "Platform", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "Total Windows", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "notEqual", "options": {"value": "Decommissioning"}}, "fieldName": "Status"}, {"config": {"id": "regex", "options": {"value": "^Windows.*"}}, "fieldName": "Platform"}], "match": "all", "type": "include"}}, {"id": "reduce", "options": {"reducers": ["count"]}}], "type": "stat"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 2, "x": 20, "y": 5}, "id": 15, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}, {"selector": "platform.display", "text": "Platform", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "Total Linux", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "notEqual", "options": {"value": "Decommissioning"}}, "fieldName": "Status"}, {"config": {"id": "regex", "options": {"value": "^(Rocky|CentOS|Ubuntu|Debian)\\s*\\d*$"}}, "fieldName": "Platform"}], "match": "all", "type": "include"}}, {"id": "reduce", "options": {"reducers": ["count"]}}], "type": "stat"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 2, "x": 22, "y": 5}, "id": 16, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}, {"selector": "platform.display", "text": "Platform", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "Total other", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "notEqual", "options": {"value": "Decommissioning"}}, "fieldName": "Status"}, {"config": {"id": "regex", "options": {"value": "^(?!Windows)(?!Rocky)(?!CentOS)(?!Ubuntu)(?!Debian).*"}}, "fieldName": "Platform"}], "match": "all", "type": "include"}}, {"id": "reduce", "options": {"reducers": ["count"]}}], "type": "stat"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "VM Name"}, "properties": [{"id": "custom.width", "value": 316}]}]}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 10}, "id": 7, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "platform.display", "text": "Platform", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}, {"selector": "status.value", "text": "Status_Value", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "Servers Under Decommission", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "equal", "options": {"value": "decommissioning"}}, "fieldName": "Status_Value"}], "match": "any", "type": "include"}}, {"id": "organize", "options": {"excludeByName": {"Status_Value": true}, "indexByName": {}, "renameByName": {}}}], "type": "table"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 6, "y": 10}, "id": 8, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "custom_fields.last_update", "text": "Last Update", "type": "string"}, {"selector": "platform.display", "text": "Platform", "type": "string"}, {"selector": "custom_fields.patch_state", "text": "Patch State", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "PROD Servers Need Patching (>60 days)", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "regex", "options": {"value": "^(202[0-4])-.*|^2025-0[1-3]-.*"}}, "fieldName": "Last Update"}, {"config": {"id": "notEqual", "options": {"value": "Decommissioning"}}, "fieldName": "Status"}], "match": "all", "type": "include"}}, {"id": "filterByValue", "options": {"filters": [{"config": {"id": "regex", "options": {"value": ".*(prod|Prod|PROD).*"}}, "fieldName": "VM Name"}], "match": "any", "type": "include"}}], "type": "table"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 10}, "id": 10, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "custom_fields.last_update", "text": "Last Update", "type": "string"}, {"selector": "platform.display", "text": "Platform", "type": "string"}, {"selector": "custom_fields.patch_state", "text": "Patch State", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "DEV Servers Need Patching (>60 days)", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "regex", "options": {"value": "^(202[0-4])-.*|^2025-0[1-3]-.*"}}, "fieldName": "Last Update"}, {"config": {"id": "notEqual", "options": {"value": "Decommissioning"}}, "fieldName": "Status"}], "match": "all", "type": "include"}}, {"id": "filterByValue", "options": {"filters": [{"config": {"id": "regex", "options": {"value": ".*(prod|Prod|PROD).*"}}, "fieldName": "VM Name"}], "match": "all", "type": "exclude"}}], "type": "table"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 18}, "id": 9, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "custom_fields.reboot_required", "text": "Reboot Required", "type": "boolean"}, {"selector": "platform.display", "text": "Platform", "type": "string"}, {"selector": "site.name", "text": "Site", "type": "string"}, {"selector": "custom_fields.Managed_by", "text": "Managed By", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "Servers Requiring Reboot - Details", "transformations": [{"id": "filterByValue", "options": {"filters": [{"config": {"id": "equal", "options": {"value": true}}, "fieldName": "Reboot Required"}, {"config": {"id": "notEqual", "options": {"value": "Decommissioning"}}, "fieldName": "Status"}], "match": "all", "type": "include"}}, {"id": "organize", "options": {"excludeByName": {"Reboot Required": true}, "indexByName": {}, "renameByName": {}}}], "type": "table"}, {"datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Status"}, "properties": [{"id": "custom.cellOptions", "value": {"type": "color-background"}}, {"id": "mappings", "value": [{"options": {"Active": {"color": "green", "index": 0}, "Decommissioning": {"color": "red", "index": 1}, "Offline": {"color": "orange", "index": 2}}, "type": "value"}]}]}]}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 26}, "id": 6, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "VM Name"}]}, "pluginVersion": "11.6.0", "targets": [{"columns": [{"selector": "name", "text": "VM Name", "type": "string"}, {"selector": "status.label", "text": "Status", "type": "string"}, {"selector": "platform.display", "text": "Platform", "type": "string"}, {"selector": "custom_fields.last_update", "text": "Last Update", "type": "string"}, {"selector": "site.name", "text": "Site", "type": "string"}, {"selector": "cluster.name", "text": "Cluster", "type": "string"}, {"selector": "role.display", "text": "Role", "type": "string"}, {"selector": "custom_fields.Managed_by", "text": "Managed By", "type": "string"}], "csv_options": {"columns": "", "comment": "", "delimiter": ",", "relax_column_count": false, "skip_empty_lines": false, "skip_lines_with_error": false}, "data": "", "datasource": {"type": "yesoreyeram-infinity-datasource", "uid": "beizcm44obw8we"}, "filters": [], "format": "table", "global_query_id": "", "refId": "A", "root_selector": "results", "source": "url", "type": "json", "url": "https://ipam.scalepoint.tech/api/virtualization/virtual-machines/?limit=1000", "url_options": {"data": "", "method": "GET"}}], "title": "Virtual Machines Details", "type": "table"}], "preload": false, "schemaVersion": 41, "tags": ["netbox", "virtualization", "infrastructure"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "NetBox Virtual Machines Dashboard", "uid": "netbox-vm-dashboard", "version": 22}