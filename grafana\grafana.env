GF_AUTH_LDAP_ENABLED=true
GF_AUTH_LDAP_CONFIG_FILE=/etc/grafana/ldap.toml
GF_SECURITY_ADMIN_USER=administrator
GF_SECURITY_ADMIN_PASSWORD=desperately9refer
GF_LOG_FILE_FORMAT=json
GF_LOG_CONSOLE_FORMAT=json
GF_LOG_MODE=console file
GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS=alexanderzobnin-zabbix-datasource
GF_PLUGINS_APP_TLS_SKIP_VERIFY_INSECURE=true
#  "GF_LOG_FILTERS=ldap:debug"
GF_SECURITY_ALLOW_EMBEDDING=true
GF_SECURITY_X_FRAME_OPTIONS=allowall

# iframe 
GF_SECURITY_CONTENT_TYPE_PROTECTION=false
GF_SECURITY_X_CONTENT_TYPE_OPTIONS=
GF_AUTH_ANONYMOUS_ENABLED=true
GF_AUTH_ANONYMOUS_ORG_ROLE=Viewer
GF_SECURITY_X_XSS_PROTECTION=false