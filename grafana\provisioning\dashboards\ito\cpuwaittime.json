{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 72, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": ["/CPU Wait Time per Dispatch-(.*)/", "$1"], "text": "replaceAlias(/CPU Wait Time per Dispatch-(.*)/, $1)", "added": false, "$$hashKey": "object:108"}, {"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": ["/scalepoint.lan(.*)/", "$1"], "text": "replaceAlias(/scalepoint.lan(.*)/, $1)", "added": false, "$$hashKey": "object:112"}, {"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)", "added": false, "$$hashKey": "object:110"}], "group": {"filter": "Sentia-cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "/CPU Wait Time per Dispatch-audit-prod.*/"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "CPU Wait Time per Dispatch-audit-prod.*", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 12}, "id": 3, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": ["/CPU Wait Time per Dispatch-(.*)/", "$1"], "text": "replaceAlias(/CPU Wait Time per Dispatch-(.*)/, $1)", "$$hashKey": "object:138"}, {"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": ["/scalepoint.lan(.*)/", "$1"], "text": "replaceAlias(/scalepoint.lan(.*)/, $1)", "$$hashKey": "object:139"}, {"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)", "$$hashKey": "object:140"}], "group": {"filter": "Sentia-cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "CPU Wait Time per Dispatch"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "CPU Wait Time per Dispatch Host", "type": "timeseries"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "CPU Wait Time", "uid": "IyMnv2QVz", "version": 4, "weekStart": ""}