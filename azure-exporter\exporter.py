#!/usr/bin/env python3

import os
import time
import logging
import requests
from datetime import datetime, timezone, timedelta
from prometheus_client import start_http_server, Gauge, Counter, Info
from dateutil.parser import parse as parse_date

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AzureADExporter:
    def __init__(self):
        self.client_id = os.getenv('AZURE_CLIENT_ID')
        self.client_secret = os.getenv('AZURE_CLIENT_SECRET')
        self.tenant_id = os.getenv('AZURE_TENANT_ID')
        
        if not all([self.client_id, self.client_secret, self.tenant_id]):
            raise ValueError("Missing required environment variables: AZURE_CLIENT_ID, AZURE_CLIENT_SECRET, AZURE_TENANT_ID")
        
        # Prometheus metrics
        self.cert_expiry_days = Gauge('azure_ad_certificate_expiry_days', 
                                    'Days until Azure AD application certificate expires',
                                    ['app_id', 'app_name', 'cert_id', 'cert_usage'])
        
        self.secret_expiry_days = Gauge('azure_ad_secret_expiry_days',
                                      'Days until Azure AD application secret expires', 
                                      ['app_id', 'app_name', 'secret_id'])
        
        self.app_info = Info('azure_ad_app_info',
                            'Azure AD application information',
                            ['app_id', 'app_name', 'app_enabled'])
        
        self.scrape_errors = Counter('azure_ad_scrape_errors_total',
                                   'Total number of scrape errors')
        
        self.last_scrape_timestamp = Gauge('azure_ad_last_scrape_timestamp',
                                         'Timestamp of last successful scrape')
        
        self.access_token = None
        self.token_expiry = None
        
    def get_access_token(self):
        """Get access token from Azure AD"""
        if self.access_token and self.token_expiry and datetime.now() < self.token_expiry:
            return self.access_token
            
        url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        
        data = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': 'https://graph.microsoft.com/.default',
            'grant_type': 'client_credentials'
        }
        
        try:
            response = requests.post(url, data=data, timeout=30)
            response.raise_for_status()
            
            token_data = response.json()
            self.access_token = token_data['access_token']
            # Token expires in seconds, add some buffer
            expires_in = token_data.get('expires_in', 3600) - 300
            self.token_expiry = datetime.now() + timedelta(seconds=expires_in)
            
            logger.info("Successfully obtained access token")
            return self.access_token
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get access token: {e}")
            self.scrape_errors.inc()
            raise
    
    def get_applications(self):
        """Get all applications from Azure AD"""
        token = self.get_access_token()
        
        url = "https://graph.microsoft.com/v1.0/applications"
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        # Select only the fields we need
        params = {
            '$select': 'id,appId,displayName,keyCredentials,passwordCredentials'
        }
        
        applications = []
        
        try:
            while url:
                response = requests.get(url, headers=headers, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                applications.extend(data.get('value', []))
                
                # Handle pagination
                url = data.get('@odata.nextLink')
                params = None  # params are included in nextLink
                
            logger.info(f"Retrieved {len(applications)} applications")
            return applications
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get applications: {e}")
            self.scrape_errors.inc()
            raise
    
    def calculate_days_to_expiry(self, end_date_str):
        """Calculate days until expiration"""
        try:
            end_date = parse_date(end_date_str)
            now = datetime.now(timezone.utc)
            
            # Ensure both dates are timezone-aware
            if end_date.tzinfo is None:
                end_date = end_date.replace(tzinfo=timezone.utc)
            if now.tzinfo is None:
                now = now.replace(tzinfo=timezone.utc)
                
            delta = end_date - now
            return max(0, delta.days)  # Don't return negative days
            
        except Exception as e:
            logger.warning(f"Failed to parse date {end_date_str}: {e}")
            return 0
    
    def update_metrics(self):
        """Update Prometheus metrics with current data"""
        try:
            applications = self.get_applications()
            
            # Clear existing metrics
            self.cert_expiry_days.clear()
            self.secret_expiry_days.clear()
            
            for app in applications:
                app_id = app.get('appId', '')
                app_name = app.get('displayName', 'Unknown')
                
                # Update app info
                self.app_info.labels(
                    app_id=app_id,
                    app_name=app_name,
                    app_enabled='true'  # Could extend this based on app status
                ).info({})
                
                # Process certificates (keyCredentials)
                for cert in app.get('keyCredentials', []):
                    if cert.get('endDateTime'):
                        days_to_expiry = self.calculate_days_to_expiry(cert['endDateTime'])
                        
                        self.cert_expiry_days.labels(
                            app_id=app_id,
                            app_name=app_name,
                            cert_id=cert.get('keyId', ''),
                            cert_usage=cert.get('usage', 'unknown')
                        ).set(days_to_expiry)
                        
                        logger.debug(f"App {app_name}: Certificate expires in {days_to_expiry} days")
                
                # Process secrets (passwordCredentials)  
                for secret in app.get('passwordCredentials', []):
                    if secret.get('endDateTime'):
                        days_to_expiry = self.calculate_days_to_expiry(secret['endDateTime'])
                        
                        self.secret_expiry_days.labels(
                            app_id=app_id,
                            app_name=app_name,
                            secret_id=secret.get('keyId', '')
                        ).set(days_to_expiry)
                        
                        logger.debug(f"App {app_name}: Secret expires in {days_to_expiry} days")
            
            self.last_scrape_timestamp.set_to_current_time()
            logger.info("Successfully updated metrics")
            
        except Exception as e:
            logger.error(f"Failed to update metrics: {e}")
            self.scrape_errors.inc()

def main():
    logger.info("Starting Azure AD Certificate/Secret Exporter")
    
    try:
        exporter = AzureADExporter()
        
        # Start HTTP server for metrics
        start_http_server(9912)
        logger.info("Metrics server started on port 9912")
        
        # Main loop
        while True:
            exporter.update_metrics()
            time.sleep(3600)  # Update every hour
            
    except KeyboardInterrupt:
        logger.info("Exporter stopped by user")
    except Exception as e:
        logger.error(f"Exporter failed: {e}")
        raise

if __name__ == '__main__':
    main()