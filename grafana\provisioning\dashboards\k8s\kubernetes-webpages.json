{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": false, "gnetId": null, "graphTooltip": 0, "id": 42, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 0}, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "(probe_http_duration_seconds { job=\"blackbox_prod_kubernetes\", instance=\"$instance\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{phase}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Webpages - https/http from PROD env and KUBERNETES app response rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 18, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"tags": [], "text": "https://myclaim.scalepoint.com", "value": "https://myclaim.scalepoint.com"}, "datasource": "prometheus", "definition": "label_values(probe_http_duration_seconds{job=\"blackbox_prod_kubernetes\"},instance)", "hide": 0, "includeAll": false, "label": "instance", "multi": false, "name": "instance", "options": [], "query": "label_values(probe_http_duration_seconds{job=\"blackbox_prod_kubernetes\"},instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Webpages - https/http from PROD env and KUBERNETES app response rate", "uid": "07TaKxOZz", "version": 6}