{%- set
  theme_colors = {
    'resolved' : '2DC72D',
    'critical' : '8C1A1A',
    'firing'   : '8C1A1A',
    'page'     : '8C1A1A',
    'severe'   : '8C1A1A',
    'warning'  : 'FF9A0B',
    'unknown'  : 'CCCCCC'
  }
-%}

{
    "@type": "MessageCard",
    "@context": "http://schema.org/extensions",
    "themeColor": "{% if status=='resolved' %} {{ theme_colors.resolved }} {% else %} {{ theme_colors[msg_text.severity] }} {% endif %}",
    "summary": "{% if status=='resolved' %}(Resolved) {% endif %}{{ msg_text.summary }}",
    "title": "Prometheus alert (int-prometheus.scalepoint.tech) {% if status=='resolved' %}(Resolved) {% elif status=='unknown' %} (status unknown) {% endif %}",
    "sections": [{
        "activityTitle": "{{ msg_text.summary }}",
        "facts": [{% if msg_text.name %}{
            "name": "Alarm",
            "value": "{{ msg_text.name }}"
        },{% endif %}{% if msg_text.instance %}{
            "name": "Target",
            "value": "{{ msg_text.instance }}"
        },{% endif %}{% if msg_text.severity %}{
            "name": "Severity",
            "value": "{{ msg_text.severity }}"
        },{% endif %}{% if msg_text.team %}{
            "name": "Team",
            "value": "{{ msg_text.team }}"
        },{% endif %}{% if msg_text.app %}{
            "name": "Application",
            "value": "{{ msg_text.app }}"
        },{% endif %}{
            "name": "Status",
            "value": "{{ msg_text.status }}"
        }],
           "markdown": true
    }],
    "potentialAction": [
    {   "@type": "OpenUri",
        "name": "Open in Prometheus",
        "targets": [
            { "os": "default", "uri": "https://int-prometheus.scalepoint.tech/alerts" }
        ]
    },
    {   "@type": "OpenUri",
        "name": "Open in Grafana",
        "targets": [
            { "os": "default", "uri": "https://int-grafana.scalepoint.tech" }
        ]
    },
    {   "@type": "OpenUri",
        "name": "Open in AlertManager",
        "targets": [
            { "os": "default", "uri": "https://int-prometheus.scalepoint.tech/alertmanager/" }
        ]
    },

    ]
}