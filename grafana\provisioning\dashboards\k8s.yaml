apiVersion: 1
providers:
- name: 'k8s'                                                         # name of this dashboard configuration (not dashboard itself)
  org_id: 1                                                                # id of the org to hold the dashboard
  folder: 'K8s'                                                       # name of the folder to put the dashboard (http://docs.grafana.org/v5.0/reference/dashboard_folders/)
  type: 'file'                                                             # type of dashboard description (json files)
  disableDeletion: false
  updateIntervalSeconds: 3600
  allowUiUpdates: false
  options:
    path: '/etc/grafana/provisioning/dashboards/k8s'       # where dashboards are