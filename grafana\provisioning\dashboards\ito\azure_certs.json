{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 113, "links": [{"asDropdown": false, "icon": "cloud", "title": "Azure Portal", "tooltip": "Open Azure Portal", "type": "link", "url": "https://portal.azure.com"}, {"asDropdown": false, "icon": "info", "title": "Prometheus Rules", "tooltip": "View Prometheus Alert Rules", "type": "link", "url": "/prometheus/rules"}], "panels": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 1}, {"color": "red", "value": 3}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "count(azure_ad_certificate_expiry_days <= 7 and azure_ad_certificate_expiry_days >= 0)", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A", "useBackend": false}], "title": "🔴 Critical Certificates (≤7 days)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 1}, {"color": "red", "value": 3}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}, "id": 2, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "count(azure_ad_secret_expiry_days <= 7 and azure_ad_secret_expiry_days >= 0)", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A", "useBackend": false}], "title": "🔴 Critical Secrets (≤7 days)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 5}, {"color": "orange", "value": 10}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}, "id": 3, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "count(azure_ad_certificate_expiry_days <= 30 and azure_ad_certificate_expiry_days > 7)", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A", "useBackend": false}], "title": "🟡 Warning Certificates (8-30 days)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 5}, {"color": "orange", "value": 10}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "count(azure_ad_secret_expiry_days <= 30 and azure_ad_secret_expiry_days > 7)", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A", "useBackend": false}], "title": "🟡 Warning Secrets (8-30 days)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "left", "cellOptions": {"type": "color-background"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red"}, {"color": "red", "value": 0}, {"color": "orange", "value": 1}, {"color": "yellow", "value": 7}, {"color": "green", "value": 30}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "app_name"}, "properties": [{"id": "displayName", "value": "Application Name"}, {"id": "custom.width", "value": 250}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cert_id"}, "properties": [{"id": "displayName", "value": "Certificate ID"}, {"id": "custom.width", "value": 280}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cert_usage"}, "properties": [{"id": "displayName", "value": "Usage"}, {"id": "custom.width", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "displayName", "value": "Days Until Expiry"}, {"id": "custom.width", "value": 120}, {"id": "unit", "value": "d"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "id": 5, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Days Until Expiry"}]}, "pluginVersion": "11.6.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "azure_ad_certificate_expiry_days <= 30", "format": "table", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A", "useBackend": false}], "title": "📋 Certificates Expiring Soon (≤30 days)", "type": "table"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "left", "cellOptions": {"type": "color-background"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red"}, {"color": "red", "value": 0}, {"color": "orange", "value": 1}, {"color": "yellow", "value": 7}, {"color": "green", "value": 30}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "app_name"}, "properties": [{"id": "displayName", "value": "Application Name"}, {"id": "custom.width", "value": 250}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "secret_id"}, "properties": [{"id": "displayName", "value": "Secret ID"}, {"id": "custom.width", "value": 280}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "displayName", "value": "Days Until Expiry"}, {"id": "custom.width", "value": 120}, {"id": "unit", "value": "d"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "id": 6, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Days Until Expiry"}]}, "pluginVersion": "11.6.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "azure_ad_secret_expiry_days <= 30", "format": "table", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A", "useBackend": false}], "title": "📋 Secrets Expiring Soon (≤30 days)", "type": "table"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "cellOptions": {"type": "color-background"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red"}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "app_name"}, "properties": [{"id": "displayName", "value": "❌ Application Name"}, {"id": "custom.width", "value": 250}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cert_id"}, "properties": [{"id": "displayName", "value": "🔒 Certificate ID"}, {"id": "custom.width", "value": 280}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "secret_id"}, "properties": [{"id": "displayName", "value": "🔑 Secret ID"}, {"id": "custom.width", "value": 280}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "displayName", "value": "🚨 STATUS"}, {"id": "mappings", "value": [{"options": {"0": {"color": "red", "index": 0, "text": "EXPIRED!"}}, "type": "value"}]}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 12}, "id": 8, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "azure_ad_certificate_expiry_days == 0", "format": "table", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A", "useBackend": false}, {"disableTextWrap": false, "editorMode": "code", "expr": "azure_ad_secret_expiry_days == 0", "format": "table", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "B", "useBackend": false}], "title": "🚨 EXPIRED Credentials - URGENT ACTION REQUIRED!", "type": "table"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Days Until Expiry", "axisPlacement": "left", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "red", "value": 0}, {"color": "yellow", "value": 7}, {"color": "green", "value": 30}]}, "unit": "d"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 20}, "id": 7, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 400}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "asc"}}, "pluginVersion": "11.6.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "azure_ad_certificate_expiry_days <= 90", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "🔒 {{app_name}} - Cert {{cert_id}}", "range": true, "refId": "A", "useBackend": false}, {"disableTextWrap": false, "editorMode": "code", "expr": "azure_ad_secret_expiry_days <= 90", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "🔑 {{app_name}} - Secret {{secret_id}}", "range": true, "refId": "B", "useBackend": false}], "title": "📈 Certificate & Secret Expiry Timeline (90 days view)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 28}, "id": 9, "options": {"displayLabels": ["app_name"], "legend": {"displayMode": "list", "placement": "right", "showLegend": true, "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "count by (app_name) ((azure_ad_certificate_expiry_days <= 30) or (azure_ad_secret_expiry_days <= 30))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "{{app_name}}", "range": false, "refId": "A", "useBackend": false}], "title": "📊 Applications with Expiring Credentials", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "green", "index": 0, "text": "ALL HEALTHY ✅"}}, "type": "value"}, {"options": {"from": 1, "result": {"color": "red", "index": 1, "text": "ISSUES DETECTED ⚠️"}, "to": 999}, "type": "range"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 28}, "id": 10, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.0", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "(count(azure_ad_certificate_expiry_days == 0) or vector(0)) + (count(azure_ad_secret_expiry_days == 0) or vector(0))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A", "useBackend": false}], "title": "🏥 Overall Health Status", "type": "stat"}], "preload": false, "refresh": "1h", "schemaVersion": 41, "tags": ["azure", "certificates", "infrastructure", "security"], "templating": {"list": []}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "🔐 Azure AD Certificates & Secrets Monitor", "uid": "fevk7cbq3l0c3d", "version": 6}