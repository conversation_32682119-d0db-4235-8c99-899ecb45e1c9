global:
  scrape_timeout: 10s
  scrape_timeout_offset: 500ms
  min_interval: 0s
  max_connections: 3
  max_idle_connections: 3

jobs:
  # All metrics from all targets get a job label, set to this value.
  - job_name: mssql

    # The set of collectors (defined below) to be applied to all targets in this job.
    collectors: [mssql_standard, mssql_custom]

    # Similar to the Prometheus configuration, multiple sets of targets may be defined, each with an optional set of
    # labels to be applied to all metrics.
    static_configs:
      - targets:
          'mssql_database_eca': 'sqlserver://zabbix_reader:Yippee3-Cha<PERSON>-<EMAIL>/eca?database=master'
          'mssql_database_shop': 'sqlserver://zabbix_reader:Yippee3-Cha<PERSON>-<EMAIL>/shop?database=master'
          'mssql_database_ecb': 'sqlserver://zabbix_reader:Yippee3-Cha<PERSON>-Ta<PERSON>@tst-sql-sen-01.scalepoint.lan/ecb?database=master'
          'mssql_database_ecc': 'sqlserver://zabbix_reader:Yippee3-Cha<PERSON>-Ta<PERSON>@tst-sql-sen-01.scalepoint.lan/ecc?database=master'
          'mssql_database_SHARED': 'sqlserver://zabbix_reader:<EMAIL>/SHARED?database=master'
          'mssql_database_INTERNAL': 'sqlserver://zabbix_reader:<EMAIL>/INTERNAL?database=master'
        labels:
          environment: production
# Collectors may be defined inline (under `collectors`) or loaded from `collector_files` (one collector per file).
collectors:
  - collector_name: mssql_standard
    metrics:
      - metric_name: mssql_log_growths
        type: counter
        help: 'Total number of times the transaction log has been expanded since last restart, per database.'
        key_labels:
          - db
        static_labels:
          env: dev
          region: europe
        values: [counter]
        query: |
          SELECT rtrim(instance_name) AS db, cntr_value AS counter
          FROM sys.dm_os_performance_counters
          WHERE counter_name = 'Log Growths' AND instance_name <> '_Total'

      - metric_name: mssql_io_stall_seconds
        type: counter
        help: 'Stall time in seconds per database and I/O operation.'
        key_labels:
          - db
        value_label: operation
        values:
          - io_stall_read
          - io_stall_write
        query_ref: io_stall

      - metric_name: mssql_io_stall_total_seconds
        type: counter
        help: 'Total stall time in seconds per database.'
        key_labels:
          - db
        values:
          - io_stall
        query_ref: io_stall

      - metric_name: mssql_hostname
        type: gauge
        help: 'Database server hostname'
        key_labels:
          - hostname
        static_value: 1
        query: |
          SELECT @@SERVERNAME AS hostname

    queries:
      - query_name: io_stall
        query: |
          SELECT
            cast(DB_Name(a.database_id) as varchar) AS db,
            sum(io_stall_read_ms) / 1000.0 AS io_stall_read,
            sum(io_stall_write_ms) / 1000.0 AS io_stall_write,
            sum(io_stall) / 1000.0 AS io_stall
          FROM
            sys.dm_io_virtual_file_stats(null, null) a
          INNER JOIN sys.master_files b ON a.database_id = b.database_id AND a.file_id = b.file_id
          GROUP BY a.database_id

  - collector_name: mssql_custom
    metrics:
      - metric_name: mssql_last_backup
        type: gauge
        help: 'Time of the last backup.'
        key_labels:
          - Database
        values: [LastBackupTime]
        query: |
          SELECT 
            name AS Database,
            MAX(backup_finish_date) AS LastBackupTime
          FROM msdb.dbo.backupset
          GROUP BY name

      - metric_name: mssql_job_status
        type: gauge
        help: 'Status of SQL agent jobs.'
        key_labels:
          - JobName
        values: [JobStatus]
        query: |
          SELECT 
            j.name AS JobName,
            CASE 
              WHEN h.run_status = 0 THEN 0
              WHEN h.run_status = 1 THEN 1
              WHEN h.run_status = 2 THEN 2
              WHEN h.run_status = 3 THEN 3
              WHEN h.run_status = 4 THEN 4
              ELSE -1
            END AS JobStatus
          FROM msdb.dbo.sysjobs AS j
          LEFT JOIN msdb.dbo.sysjobhistory AS h ON j.job_id = h.job_id
          WHERE h.instance_id = (
            SELECT MAX(instance_id)
            FROM msdb.dbo.sysjobhistory
            WHERE job_id = j.job_id
          )

      - metric_name: mssql_buffer_cache_hit_ratio
        type: gauge
        help: 'Buffer cache hit ratio.'
        values: [BufferCacheHitRatio]
        query: |
          SELECT 
            (CASE 
              WHEN d.cntr_value = 0 THEN 0 
              ELSE CAST(a.cntr_value AS FLOAT) / CAST(b.cntr_value AS FLOAT) * 100
            END) AS BufferCacheHitRatio
          FROM 
            sys.dm_os_performance_counters AS a
            JOIN sys.dm_os_performance_counters AS b ON a.object_name = b.object_name
            JOIN sys.dm_os_performance_counters AS d ON a.object_name = d.object_name
          WHERE 
            a.counter_name = 'Buffer cache hit ratio' 
            AND b.counter_name = 'Buffer cache hit ratio base' 
            AND d.counter_name = 'Page life expectancy'

      - metric_name: mssql_service_unavailable
        type: gauge
        help: 'The TCP port of the MSSQL Server service is currently unavailable.'
        values: [ServiceUnavailable]
        query: |
          SELECT 
            CASE 
              WHEN NOT EXISTS (
                SELECT 1
                FROM sys.dm_exec_connections
                WHERE local_net_address = 'localhost' AND local_tcp_port = 1433
              ) THEN 1 
              ELSE 0 
            END AS ServiceUnavailable

      - metric_name: mssql_version_changed
        type: gauge
        help: 'MSSQL version has changed. Acknowledge to close the problem manually.'
        values: [VersionChanged]
        query: |
          WITH CurrentVersion AS (
            SELECT SERVERPROPERTY('ProductVersion') AS version
          ),
          PreviousVersion AS (
            SELECT TOP 1 version
            FROM version_history
            ORDER BY check_time DESC
          )
          SELECT 
            CASE 
              WHEN (SELECT version FROM CurrentVersion) <> (SELECT version FROM PreviousVersion)
              THEN 1 
              ELSE 0 
            END AS VersionChanged

      - metric_name: mssql_service_restarted
        type: gauge
        help: 'Uptime is less than 10 minutes.'
        values: [ServiceRestarted]
        query: |
          SELECT 
            CASE 
              WHEN (SELECT sqlserver_start_time FROM sys.dm_os_sys_info) > DATEADD(MINUTE, -10, GETDATE()) THEN 1 
              ELSE 0 
            END AS ServiceRestarted

      - metric_name: mssql_failed_fetch_info
        type: gauge
        help: 'Zabbix has not received any data for items for the last 30 minutes.'
        values: [FailedFetchInfo]
        query: |
          SELECT 
            CASE 
              WHEN NOT EXISTS (
                SELECT 1
                FROM sys.dm_exec_requests
                WHERE start_time > DATEADD(MINUTE, -30, GETDATE())
              ) THEN 1 
              ELSE 0 
            END AS FailedFetchInfo

      - metric_name: mssql_too_frequently_using_pointers
        type: gauge
        help: 'Too frequently using pointers.'
        values: [TooFrequentlyUsingPointers]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT cntr_value
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Forwarded Records/sec'
              ) * 100 > 10 * (
                SELECT cntr_value
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Batch Requests/sec'
              ) THEN 1 
              ELSE 0 
            END AS TooFrequentlyUsingPointers

      - metric_name: mssql_high_work_files_created
        type: gauge
        help: 'Number of work files created per second is high.'
        values: [HighWorkFilesCreated]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Workfiles Created/sec'
              ) > 20 THEN 1 
              ELSE 0 
            END AS HighWorkFilesCreated

      - metric_name: mssql_high_work_tables_created
        type: gauge
        help: 'Number of work tables created per second is high.'
        values: [HighWorkTablesCreated]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Worktables Created/sec'
              ) > 20 THEN 1 
              ELSE 0 
            END AS HighWorkTablesCreated

      - metric_name: mssql_low_work_table_cache
        type: gauge
        help: 'Percentage of work tables available from the work table cache is low.'
        values: [LowWorkTableCache]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MAX(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Worktables From Cache Ratio'
              ) < 90 THEN 1 
              ELSE 0 
            END AS LowWorkTableCache

      - metric_name: mssql_low_buffer_cache_efficiency
        type: gauge
        help: 'Percentage of the buffer cache efficiency is low.'
        values: [LowBufferCacheEfficiency]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MAX(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Buffer cache hit ratio'
              ) < 30 THEN 1 
              ELSE 0 
            END AS LowBufferCacheEfficiency

      - metric_name: mssql_low_buffer_cache_efficiency_warning
        type: gauge
        help: 'Low buffer cache hit ratio.'
        values: [LowBufferCacheEfficiencyWarning]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MAX(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Buffer cache hit ratio'
              ) < 50 THEN 1 
              ELSE 0 
            END AS LowBufferCacheEfficiencyWarning

      - metric_name: mssql_high_free_page_waits
        type: gauge
        help: 'Number of rps waiting for a free page is high.'
        values: [HighFreePageWaits]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Free list stalls/sec'
              ) > 2 THEN 1 
              ELSE 0 
            END AS HighFreePageWaits

      - metric_name: mssql_high_lazy_writer_buffers
        type: gauge
        help: 'Number of buffers written per second by the lazy writer is high.'
        values: [HighLazyWriterBuffers]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Lazy writes/sec'
              ) > 20 THEN 1 
              ELSE 0 
            END AS HighLazyWriterBuffers

      - metric_name: mssql_low_page_life_expectancy
        type: gauge
        help: 'Page life expectancy is low.'
        values: [LowPageLifeExpectancy]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MAX(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Page life expectancy'
              ) < 300 THEN 1 
              ELSE 0 
            END AS LowPageLifeExpectancy

      - metric_name: mssql_high_physical_page_reads
        type: gauge
        help: 'Number of physical database page reads per second is high.'
        values: [HighPhysicalPageReads]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Page reads/sec'
              ) > 90 THEN 1 
              ELSE 0 
            END AS HighPhysicalPageReads

      - metric_name: mssql_high_physical_page_writes
        type: gauge
        help: 'Number of physical database page writes per second is high.'
        values: [HighPhysicalPageWrites]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Page writes/sec'
              ) > 90 THEN 1 
              ELSE 0 
            END AS HighPhysicalPageWrites

      - metric_name: mssql_too_many_physical_reads
        type: gauge
        help: 'Too many physical reads occurring.'
        values: [TooManyPhysicalReads]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT cntr_value
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Readahead pages/sec'
              ) > 20 / 100 * (
                SELECT cntr_value
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Page reads/sec'
              ) THEN 1 
              ELSE 0 
            END AS TooManyPhysicalReads

      - metric_name: mssql_high_average_wait_time
        type: gauge
        help: 'Total average wait time for locks is high.'
        values: [HighAverageWaitTime]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Average Wait Time (ms)'
              ) > 500 THEN 1 
              ELSE 0 
            END AS HighAverageWaitTime

      - metric_name: mssql_high_lock_requests
        type: gauge
        help: 'Total number of locks per second is high.'
        values: [HighLockRequests]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Lock Requests/sec'
              ) > 1000 THEN 1 
              ELSE 0 
            END AS HighLockRequests

      - metric_name: mssql_high_lock_timeouts
        type: gauge
        help: 'Total lock requests per second that timed out is high.'
        values: [HighLockTimeouts]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Lock Timeouts/sec'
              ) > 1 THEN 1 
              ELSE 0 
            END AS HighLockTimeouts

      - metric_name: mssql_some_blocking_occurring
        type: gauge
        help: 'Some blocking is occurring for 5m.'
        values: [SomeBlockingOccurring]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Lock Waits/sec'
              ) > 0 THEN 1 
              ELSE 0 
            END AS SomeBlockingOccurring

      - metric_name: mssql_high_number_of_deadlocks
        type: gauge
        help: 'Number of deadlocks is high.'
        values: [HighNumberOfDeadlocks]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Number of Deadlocks/sec'
              ) > 1 THEN 1 
              ELSE 0 
            END AS HighNumberOfDeadlocks

      - metric_name: mssql_high_percent_adhoc_queries
        type: gauge
        help: 'Percent of ad hoc queries running is high.'
        values: [HighPercentAdhocQueries]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'SQL Compilations/sec'
              ) > 10 THEN 1 
              ELSE 0 
            END AS HighPercentAdhocQueries

      - metric_name: mssql_high_percent_recompiles
        type: gauge
        help: 'Percent of times statement recompiles is high.'
        values: [HighPercentRecompiles]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'SQL Re-Compilations/sec'
              ) > 10 THEN 1 
              ELSE 0 
            END AS HighPercentRecompiles

      - metric_name: mssql_high_index_table_scans
        type: gauge
        help: 'Number of index and table scans exceeds index searches in the last 15m.'
        values: [HighIndexTableScans]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Full Scans/sec'
              ) > 0.001 THEN 1 
              ELSE 0 
            END AS HighIndexTableScans
