{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Gdańsk, Kharkov, Lublin", "editable": false, "gnetId": null, "graphTooltip": 0, "id": 26, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 0}, "id": 1, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["fortigate-lub.scalepoint.com"], "text": "setAlias(fortigate-lub.scalepoint.com)"}], "group": {"filter": "Network devices"}, "host": {"filter": "fg_LUB"}, "item": {"filter": "Current CPU Util"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["fortigate-kha.scalepoint.com"], "text": "setAlias(fortigate-kha.scalepoint.com)"}], "group": {"filter": "Network devices"}, "host": {"filter": "fg_UA"}, "item": {"filter": "Current CPU Util"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["fortigate-gda.scalepoint.com"], "text": "setAlias(fortigate-gda.scalepoint.com)"}], "group": {"filter": "Network devices"}, "host": {"filter": "fg_GDA"}, "item": {"filter": "Current CPU Util"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "C", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 95}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Fortigate CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 0}, "id": 4, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["fortigate-lub.scalepoint.com"], "text": "setAlias(fortigate-lub.scalepoint.com)"}], "group": {"filter": "Network devices"}, "host": {"filter": "fg_LUB"}, "item": {"filter": "Current connections"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["fortigate-kha.scalepoint.com"], "text": "setAlias(fortigate-kha.scalepoint.com)"}], "group": {"filter": "Network devices"}, "host": {"filter": "fg_UA"}, "item": {"filter": "Current connections"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["fortigate-gda.scalepoint.com"], "text": "setAlias(fortigate-gda.scalepoint.com)"}], "group": {"filter": "Network devices"}, "host": {"filter": "fg_GDA"}, "item": {"filter": "Current connections"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "C", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Current connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 6}, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Network Traffic"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Network devices"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "/.*Upload VPN*/"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "VPN Upload", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 6}, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Network Traffic"}, "functions": [], "group": {"filter": "Network devices"}, "host": {"filter": "/.*/"}, "item": {"filter": "/.*Download VPN*/"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "VPN Download", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 11}, "id": 5, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Network Traffic"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Network devices"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "Download wan1"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "Network Traffic"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Network devices"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "Upload wan1"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Panel Title", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 16, "style": "dark", "tags": ["fortigate", "utm", "zabbix"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Fortigate - UTM in branch offices", "uid": "000000073", "version": 5}