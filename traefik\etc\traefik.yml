global:
  checkNewVersion: false
log:
  level: INFO
  filePath: "/var/log/traefik/debug.log"
  format: json
accessLog:
  filePath: "/var/log/traefik/access.log"
  format: json
  fields:
    defaultMode: keep
    headers:
      defaultMode: keep
defaultEntryPoints:
   - http
   - https
api:
  dashboard: true
  insecure: false
ping: {}
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
  file:
    filename: ./traefik.yml
    watch: true
entryPoints:
  http:
    address: ":80"
    http:
      redirections:
        entryPoint:
          to: https
          scheme: https
  https:
    address: ":443"
  metrics:
    address: ":8083"
  traefik:
    address: ":8082"
  http-9092:
    address: ":9092"
    http:
      redirections:
        entryPoint:
          to: https-9091
          scheme: https
  https-9091:
    address: ":9091"
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /etc/traefik/acme/acme.json
      dnsChallenge:
        # used during the challenge
        provider: "cloudflare"
        delaybeforecheck: 60
        resolvers:
          - "*******:53"
          - "*******:53"
# use for test runs
  letsencrypt-stage:
    acme:
      email: <EMAIL>
      storage: /etc/traefik/acme/acme-stage.json
      caServer: https://acme-staging-v02.api.letsencrypt.org/directory
      dnsChallenge:
        # used during the challenge
        provider: "cloudflare"
        delaybeforecheck: 60
        resolvers:
          - "*******:53"
          - "*******:53"
tls:
  options:
    default:
      minVersion: VersionTLS12
      cipherSuites:
        - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305
        - TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305
      sniStrict: false
# hsts
http:
  middlewares:
    hsts-header:
      headers:
        customResponseHeaders:
          Strict-Transport-Security: max-age=********
        stsseconds: ********
        stspreload: true
        stsincludesubdomains: false
    # iframe
    csp-header:
      headers:
        customResponseHeaders:
          Content-Security-Policy: "frame-ancestors *;"

metrics:
  prometheus:
    manualRouting: true
    entryPoint: metrics
pilot:
    token: "2eed905f-3a4c-49f3-bd02-b06a9f74e5c6"
