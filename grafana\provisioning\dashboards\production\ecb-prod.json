{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 68, "panels": [], "title": "G1", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 1}, "height": "", "hiddenSeries": false, "id": 57, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:862"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:863"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe101.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe101.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe101 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 1}, "height": "", "hiddenSeries": false, "id": 59, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:779"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:780"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe102.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe102.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe102 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 1}, "height": "", "hiddenSeries": false, "id": 58, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:696"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:697"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe103.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe103.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe103 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 1}, "height": "", "hiddenSeries": false, "id": 60, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:613"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:614"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe104.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe104.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe104 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 66, "title": "G2 - ECB Prod Taksatorringen nodes", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 9}, "height": "", "hiddenSeries": false, "id": 34, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:128", "def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)"}, {"$$hashKey": "object:129", "def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe201.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe201.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe201.scalepoint.lan 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 9}, "height": "", "hiddenSeries": false, "id": 52, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:187", "def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)"}, {"$$hashKey": "object:188", "def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe202.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe202.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe202.scalepoint.lan 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 9}, "height": "", "hiddenSeries": false, "id": 49, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:122"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:123"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe203.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe203.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe203.scalepoint.lan 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 9}, "height": "", "hiddenSeries": false, "id": 51, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:353", "def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)"}, {"$$hashKey": "object:354", "def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe204.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe204.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe204.scalepoint.lan 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 64, "panels": [], "title": "G3", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 17}, "height": "", "hiddenSeries": false, "id": 69, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:1158"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:1159"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe301.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe301.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe301 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 17}, "height": "", "hiddenSeries": false, "id": 70, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:1241"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:1242"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe302.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe302.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe302 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 17}, "height": "", "hiddenSeries": false, "id": 71, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:1407"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:1408"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe303.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe303.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe303 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 17}, "height": "", "hiddenSeries": false, "id": 72, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:1490"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:1491"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe304.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe304.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe304 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 62, "panels": [], "title": "G4", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 25}, "height": "", "hiddenSeries": false, "id": 73, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:1589"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:1590"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe401.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe401.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe401 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 25}, "height": "", "hiddenSeries": false, "id": 74, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:1672"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:1673"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe402.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe402.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe402 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 25}, "height": "", "hiddenSeries": false, "id": 75, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:1755"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:1756"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe403.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe403.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe403 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 25}, "height": "", "hiddenSeries": false, "id": 76, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:1838"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:1839"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe404.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-cwa-wfe404.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-cwa-wfe404 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 32}, "id": 47, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "refId": "A"}], "title": "ECB Prod  regular nodes", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 4, "x": 0, "y": 33}, "height": "", "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:802", "alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:1491", "def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)"}, {"$$hashKey": "object:1492", "def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-ecb-wfe01.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-ecb-wfe01.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-ecb-wfe01 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 5, "x": 4, "y": 33}, "height": "", "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:1574", "def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)"}, {"$$hashKey": "object:1575", "def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-ecb-wfe02.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-ecb-wfe02.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-ecb-wfe02 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 5, "x": 9, "y": 33}, "height": "", "hiddenSeries": false, "id": 25, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)", "$$hashKey": "object:186"}, {"def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)", "$$hashKey": "object:187"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-ecb-wfe09.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-ecb-wfe09.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-ecb-wfe03 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 5, "x": 14, "y": 33}, "height": "", "hiddenSeries": false, "id": 27, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:1740", "def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)"}, {"$$hashKey": "object:1741", "def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-ecb-wfe04.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-ecb-wfe04.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-ecb-wfe04 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 5, "x": 19, "y": 33}, "height": "", "hiddenSeries": false, "id": 48, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:1823", "def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["CPU Usage - 7 days"], "text": "setAlias(CPU Usage - 7 days)"}, {"$$hashKey": "object:1824", "def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-ecb-wfe05.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": true, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "prod-ecb-wfe05.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-ecb-wfe05 7 day shift", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 40}, "id": 45, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "refId": "A"}], "title": "ECB Prod", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 41}, "height": "", "hiddenSeries": false, "id": 36, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:2155", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan(.*)/", "- current - CPU Usage"], "text": "replaceAlias(/.scalepoint.lan(.*)/, - current - CPU Usage)"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "/prod-ecb-wfe.*/"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:2176", "def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)"}, {"$$hashKey": "object:2177", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan(.*)/", " - 7 day shift - CPU Usage"], "text": "replaceAlias(/.scalepoint.lan(.*)/,  - 7 day shift - CPU Usage)"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "/prod-ecb-wfe.*/"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-ecb-wfe 7 day shift - CPU Usage", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 52}, "height": "", "hiddenSeries": false, "id": 53, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 4, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "CPU Usage", "fill": 6}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:440", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan(.*)/", "- current - CPU Usage"], "text": "replaceAlias(/.scalepoint.lan(.*)/, - current - CPU Usage)"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "/prod-cwa-wfe.*/"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "CPU"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:461", "def": {"category": "Time", "defaultParams": ["24h"], "name": "timeShift", "params": [{"name": "interval", "options": ["24h", "7d", "1M", "+24h", "-24h"], "type": "string"}]}, "params": ["-7d"], "text": "timeShift(-7d)"}, {"$$hashKey": "object:462", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan(.*)/", " - 7 day shift - CPU Usage"], "text": "replaceAlias(/.scalepoint.lan(.*)/,  - 7 day shift - CPU Usage)"}], "group": {"filter": "Windows Servers"}, "hide": false, "host": {"filter": "/prod-cwa-wfe.*/"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "/prod-cwa-wfe.*/ 7 day shift - CPU Usage", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "label": "", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 0, "y": 63}, "id": 32, "options": {"displayMode": "gradient", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": "Status"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)", "$$hashKey": "object:979"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.lan: Uptime", ":"], "text": "replaceAlias(.scalepoint.lan: Uptime, :)", "$$hashKey": "object:980"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/prod-cwa*/"}, "item": {"filter": "Uptime"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "System uptime", "type": "bargauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 10, "x": 4, "y": 63}, "hiddenSeries": false, "id": 29, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Filesystem C:"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:2263", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.lan: C:: Used space", ":"], "text": "replaceAlias(.scalepoint.lan: C:: Used space, :)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/.*prod-ecb-wfe*/"}, "item": {"filter": "C:: Used space"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Used space on C:", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 10, "x": 14, "y": 63}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:1601", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan: Number of processes(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.lan: Number of processes(.*)/, $1)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/.*prod-ecb-wfe*/"}, "item": {"filter": "Number of processes"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 300}, {"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 400}], "timeRegions": [], "title": "Number of processes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:117", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:118", "format": "none", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 0, "y": 71}, "id": 54, "options": {"displayMode": "gradient", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": "Status"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:522", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:523", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.lan: Uptime", ":"], "text": "replaceAlias(.scalepoint.lan: Uptime, :)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/prod-cwa-wfe.*/"}, "item": {"filter": "Uptime"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "System uptime", "type": "bargauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 10, "x": 4, "y": 71}, "hiddenSeries": false, "id": 55, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Filesystem C:"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:556", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.lan: C:: Used space", ":"], "text": "replaceAlias(.scalepoint.lan: C:: Used space, :)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/prod-cwa-wfe.*/"}, "item": {"filter": "C:: Used space"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Used space on C:", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 10, "x": 14, "y": 71}, "hiddenSeries": false, "id": 56, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:612", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.lan: Number of processes(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.lan: Number of processes(.*)/, $1)"}], "group": {"filter": "Windows Servers"}, "host": {"filter": "/prod-cwa-wfe.*/"}, "item": {"filter": "Number of processes"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 300}, {"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 400}], "timeRegions": [], "title": "Number of processes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:117", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:118", "format": "none", "logBase": 1, "show": false}], "yaxis": {"align": false}}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": ["ecb", "cpu", "zabbix"], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "ECB - Prod", "uid": "I6sP7Cwmk", "version": 7, "weekStart": ""}