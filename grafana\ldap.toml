[[servers]]
host = "spcph.local"
port = 389
use_ssl = false
start_tls = false
ssl_skip_verify = true
bind_dn = "SPCPH\\%s"
search_filter = "(sAMAccountName=%s)"
search_base_dns = ["dc=spcph,dc=local"]

[servers.attributes]
name = "givenName"
surname = "sn"
username = "sAMAccountName"
member_of = "memberOf"
email =  "mail"

[[servers.group_mappings]]
group_dn = "CN=Domain Admins,CN=Users,DC=spcph,DC=local"
org_role = "Admin"
grafana_admin = true

[[servers.group_mappings]]
group_dn = "CN=ITOS,OU=Distribution Groups,OU=Service Groups,DC=spcph,DC=local"
org_role = "Admin"
grafana_admin = true


[[servers.group_mappings]]
group_dn = "*"
org_role = "Viewer"