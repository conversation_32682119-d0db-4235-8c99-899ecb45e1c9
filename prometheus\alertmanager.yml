global:
  # The smarthost and SMTP sender used for mail notifications.
  smtp_smarthost: 'mailrelay-ext.scalepoint.lan:587'
  smtp_from: '<EMAIL>'
  smtp_require_tls: false
  opsgenie_api_key: ************************************

#opsgenie_config:
#  send_resolved: true
#  api_key:
#  api_url: <string> | default = global.opsgenie_api_url ]
#  message: <tmpl_string>
#  description: <tmpl_string> | default = '{{ template "opsgenie.default.description" . }}' ]
#  source: <tmpl_string> | default = '{{ template "opsgenie.default.source" . }}' ]
#  details: { <string>: <tmpl_string>, ... }
#  responders:
#    - <responder>
#  tags: <tmpl_string>
#  note: <tmpl_string>
# Priority level of alert. Possible values are P1, P2, P3, P4, and P5.
#  priority: <tmpl_string>
# The HTTP client's configuration.
#  http_config: <http_config> | default = global.http_config ]

route:
  repeat_interval: 1m
  receiver: mails
  routes:
    - match:
        severity: page
      continue: true
      receiver: mails
    - match:
        severity: page
      continue: true
      receiver: mails_itos
    - match:
        severity: page
      continue: true
      receiver: prom2teams
    - match:
        severity: page
      continue: true
      receiver: opsgenie
      group_by: [...]
receivers:
#- name: msteams
#  email_configs:
#    - to: '<EMAIL>'
#      smarthost: mailrelay-ext.scalepoint.lan:587
#      send_resolved: true
- name: mails
  email_configs:
    - to: '<EMAIL>'
      smarthost: mailrelay-ext.scalepoint.lan:587
      send_resolved: true
- name: mails_itos
  email_configs:
    - to: '<EMAIL>'
      smarthost: mailrelay-ext.scalepoint.lan:587
      send_resolved: true
- name: prom2teams
  webhook_configs:
    - url: http://prom2teams:8089/v2/Connector
      send_resolved: true
- name: opsgenie
  opsgenie_configs:
    - api_key: aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
      responders:
        - name: ITOS-general
          type: team
      send_resolved: true
      tags: '{{ .GroupLabels.env }},{{ .GroupLabels.instance }},{{ .GroupLabels.team }}'
