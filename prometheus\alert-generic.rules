# YAML
groups:
- name: graylog
  rules:
  - alert: Graylog - high messages input count for last 5 minutes
    expr: (sum(rate(org_graylog2_throughput_input {job!~"^(gr-dev|gr-ito-graylog).*"} [5m])) by (job)) > 6000
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
    annotations:
      summary: 'Current value is: {{ $value | printf "%.2f" }} on job {{ $labels.job }} - lots of traffic per second - check what is spamming'
      job: "{{ $labels.job }}"
  - alert: Graylog - high uncommited messages count for last 5 minutes
    expr: (sum by (job) (org_graylog2_journal_entries_uncommitted {job!~"^(gr-dev|gr-ito-graylog).*"})) > 1000000
    for: 5m
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
    annotations:
      summary: 'Current value is: {{ $value | printf "%.2f" }} on job {{ $labels.job }} - some node is throttled or has no time to process'
      job: "{{ $labels.job }}"
  - alert: Graylog - node is down
    expr: up{app="graylog",team="ito", job!~"^(gr-dev|gr-ito-graylog).*"} < 1
    for: 15m
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
      instance: "{{ $labels.instance }}"
    annotations:
      summary: 'Node: {{ $labels.instance }} is down in job: {{ $labels.job }} - for some reason node is down, investigate'
      job: "{{ $labels.job }}"
# elasticsearch
- name: elastic global
  rules:
  - alert: Elasticsearch - Cluster status is yellow
    expr: elasticsearch_cluster_health_status{color="yellow", env!~"pjt|edu"} > 0
    for: 20m
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
    annotations:
      summary: "Flapping elastic on {{ $labels.cluster }} - if it stays for longer time, check if nodes arent down"
      cluster: "{{ $labels.cluster }}"
  - alert: Elasticsearch - Cluster status is red
    expr: elasticsearch_cluster_health_status{color="red"} > 0
    for: 2m
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
    annotations:
      summary: "Critical elastic on {{ $labels.cluster }} - you should have at least 2 node up. This is bad, bad, bad, investigate , try this: https://confluence.scalepoint.com/spaces/IT/pages/261656063/CI-WRK-06+Elasticsearch+cluster+health+red+-+unassigned+shards"
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
  - alert: Elasticsearch - Number of healthy nodes is too low
    expr: (elasticsearch_cluster_health_number_of_nodes{env!~"(pjt|edu)"} < 3) or (elasticsearch_cluster_health_number_of_nodes{cluster=~"(graylog-prod01).*"} < 4)
    for: 15m
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
    annotations:
      summary: "Flapping elastic on {{ $labels.cluster }} - check why nodes count is too low. Is it down/crashed? Investigate"
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
  - alert: Elasticsearch - active shards/primary shards ratio is below 2 - no replicas or cluster is rebalancing
    expr: sum (elasticsearch_cluster_health_active_shards {env="prod"}/elasticsearch_cluster_health_active_primary_shards {env="prod"}) by (job) < 2 OR sum (elasticsearch_cluster_health_active_shards {env="prod"}/elasticsearch_cluster_health_active_primary_shards {env="prod"}) by (job) != sum (elasticsearch_cluster_health_active_shards {env="prod"}/elasticsearch_cluster_health_active_primary_shards {env="prod"}) by (job)
    for: 2m
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
      job: "{{ $labels.job }}"
    annotations:
      summary: "Elasticsearch - active shards/primary shards ratio is below 2 - no replicas or cluster is rebalancing on {{ $labels.cluster }}. This is aftermath after node went down, wait and see"
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
      job: "{{ $labels.job }}"
  # Query time
  - alert: Elasticsearch - query time exceeds 10s for last 10 minutes
    expr: irate(elasticsearch_indices_search_query_time_seconds[10m]) >10
    for: 10m
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
      job: "{{ $labels.job }}"
    annotations:
      summary: "Query time exceeds 10s - slow IO, low mem, failing disks or overuse by application {{ $labels.cluster }}. This is bad, as longer query time equals more timeouts. Investigate"
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
      job: "{{ $labels.job }}"
  # Rejected transactions are rising
  # - alert: Elasticsearch - thread pool reject is rising for last 20 minutes
  #   expr: (rate(elasticsearch_thread_pool_rejected_count {env=~"prod|ito|test"} [20m])) > 0.2
  #   for: 20m
  #   labels:
  #     severity: page
  #     cluster: "{{ $labels.cluster }}"
  #     app: "{{ $labels.app }}"
  #     team: "{{ $labels.team }}"
  #     env: "{{ $labels.env }}"
  #     job: "{{ $labels.job }}"
  #     host: "{{ $labels.host }}"
  #     type: "{{ $labels.type }}"
  #   annotations:
  #     summary: "Elasticsearch - thread pool reject is rising for last 5 minutes, low mem, failing disks or overuse by application {{ $labels.cluster }}. This is bad, as rejects equals timeouts. Investigate"
  #     cluster: "{{ $labels.cluster }}"
  #     app: "{{ $labels.app }}"
  #     team: "{{ $labels.team }}"
  #     env: "{{ $labels.env }}"
  #     job: "{{ $labels.job }}"
  #     host: "{{ $labels.host }}"
  #     type: "{{ $labels.type }}"
# queue size
  - alert: Elasticsearch - thread pool queue count higher than usual
    expr: (elasticsearch_thread_pool_queue_count {env=~"(prod)",type="write"}) > 200
    for: 5m
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
      job: "{{ $labels.job }}"
      host: "{{ $labels.host }}"
      type: "{{ $labels.type }}"
    annotations:
      summary: "Elasticsearch -  thread pool queue count higher than usual: low mem, failing disks or overuse by application {{ $labels.cluster }}. This is bad, as rejects equals timeouts. Investigate"
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
      job: "{{ $labels.job }}"
      host: "{{ $labels.host }}"
      type: "{{ $labels.type }}"
  - alert: Elasticsearch - Health  (up) of a cluster/job response is 0 instead of 1
    expr: elasticsearch_cluster_health_up {env="prod"} == 0
    for: 10m
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
    annotations:
      summary: "Elasticsearch - Health  (up) of a cluster/job response is 0 instead of 1 {{ $labels.cluster }}. Is node down?"
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
# free space on elastic nodes below 10%
  - alert: Elasticsearch - Free space for data nodes below 10%
    expr: round((elasticsearch_filesystem_data_free_bytes{env="prod"}/elasticsearch_filesystem_data_size_bytes{env="prod"})*100)<10
    for: 15m
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
      host: "{{ $labels.host }}"
    annotations:
      summary: "Elasticsearch - Free space for data nodes below 10%. Check free space on nodes, check ratio in which data are flowing"
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "{{ $labels.env }}"
      host: "{{ $labels.host }}"
# RabbitMQ Nodes
- name: rabbitmq nodes alert
  rules:
  - alert: RabbitMQ - Nodes down
    expr: rabbitmq_running < 1
    for: 5m
    labels:
      severity: page
      instance: "{{ $labels.job }}"
      node:  "{{ $labels.node }}"
    annotations:
      summary: "Failling rabbitmq node {{ $labels.node }} on {{ $labels.job }}"
      instance: "{{ $labels.job }}"
  # Let's check how many nodes are in partition
  - alert: RabbitMQ - Nodes count less than 3
    expr: count(rabbitmq_partitions) by (instance) < 3
    for: 1m
    labels:
      severity: page
      instance: "{{ $labels.job }}"
      node:  "{{ $labels.node }}"
    annotations:
      summary: "Less nodes than 3 on {{ $labels.job }}"
      instance: "{{ $labels.job }}"
# Blackbox
- name: Blackbox related alerts
  rules:
  # ENV=ITO
  - alert: Webpages - https/http from Internal/ITO env probe failed
    expr: probe_success{env="ito",app="website"} != 1
    for: 3m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
    annotations:
      summary: 'Webpage from Internal/ITO env probe failed: {{ $labels.instance }}'
  # ENV=DEV
  - alert: Webpages - https/http from DEV env probe failed
    expr: probe_success{env="dev",app="website"} != 1
    for: 30m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
    annotations:
      summary: 'Webpage from DEV env probe failed: {{ $labels.instance }}'
  # ENV=QA
  - alert: Webpages - https/http from QA env probe failed
    expr: probe_success{env="qa",app="website"} != 1
    for: 30m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
    annotations:
      summary: 'Webpage from QA env probe failed: {{ $labels.instance }}'
  # ENV=PJT
  - alert: Webpages - https/http from  PJT env probe failed
    expr: probe_success{env="pjt",app="website"} != 1
    for: 10m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
    annotations:
      summary: 'Webpage from PJT env probe failed: {{ $labels.instance }}'
  # ENV=PROD
  - alert: Webpages - https/http from PROD env probe failed
    expr: probe_success{job="blackbox_prod",app="website"} != 1
    for: 3m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
    annotations:
      summary: 'Webpages - https/http from PROD env probe failed {{ $labels.instance }} last value: {{ $value | printf "%.2f" }}'
# Blackbox DNS
  # DNS
  - alert: DNS - (records or server) are not avaiable
    expr: probe_success{job="blackbox_dns",app="dns"} < 1
    for: 2m
    labels:
#      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      instance: "{{ $labels.instance }}"
      module: "{{ $labels.module }}"
    annotations:
      summary: 'DNS - records of {{ $labels.module }} on server {{ $labels.instance }} not available  last value: {{ $value | printf "%.2f" }}'
# Blackbox VPNs
  - alert: VPNs - VPN https/http  probe failed
    expr: probe_success{env="ito",app="vpn"} != 1
    for: 10m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
    annotations:
      summary: 'VPNs - VPN https/http  probe failed: {{ $labels.instance }}'

# Redis
- name: Redis health alerts
  rules:
  - alert: Redis - service not available
    expr: redis_up <1
    for: 5m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
      instance: "{{ $labels.instance }}"
    annotations:
      summary: "Redis service not available, instance: {{ $labels.instance }}"
  - alert: Redis - too much connected clients
    expr: (redis_connected_clients {env!="prod"} >1900) or (redis_connected_clients {env="prod"} >2500)
    for: 15m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
      instance: "{{ $labels.instance }}"
    annotations:
      summary: 'Redis - too much connected clients, instance: {{ $labels.instance }} last value: {{ $value | printf "%.2f" }}. Increase or separate tiers'
# Synology
- name: Synology status and health
  rules:
  - alert: Synology box not available for 5m
    expr: up{app="node_exporter", job=~"^syn-.*"} <1
    for: 5m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
      instance: "{{ $labels.instance }}"
    annotations:
      summary: "Synology box service not available, instance: {{ $labels.instance }}"
  - alert: Synology volume is dangerously low on space
    expr: |
      round(100 - (100 * (node_filesystem_avail_bytes{mountpoint!="/", fstype!="rootfs", source="synology", device!~"/dev/mapper/cachedev_(0|1)"} / node_filesystem_size_bytes{mountpoint!="/", fstype!="rootfs", source="synology", device!~"/dev/mapper/cachedev_(0|1)"}))) > 85
    for: 20m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
      instance: "{{ $labels.instance }}"
      device: "{{ $labels.device }}"
      fstype: "{{ $labels.fstype }}"
    annotations:
      summary: "Synology volume {{ $labels.device }} is dangerously low on space {{ $labels.instance }}. Free some space, as this is dangerous for a backup jobs"
- name: TLS Certificate expiry
  rules:
    - alert: TLS Certificate expiry
      expr: ((probe_ssl_earliest_cert_expiry - time() < 86400 * 20) or (certmanager_certificate_expiration_timestamp_seconds - time() < 86400 * 20)) and (hour() >= 9 and hour() < 17) and (day_of_week() >= 1 and day_of_week() <= 5)
      for: 30m
      labels:
        severity: page
        job: "{{ $labels.job }}"
        env: "{{ $labels.env }}"
        app: "{{ $labels.app }}"
        cluster: "{{ $labels.cluster }}"
        cluster_env: "{{ $labels.cluster_env }}"
        namespace: "{{ $labels.exported_namespace }}"
      annotations:
        description: "TLS/SSL certificate will expire in {{ $value | humanizeDuration }} (instance: {{ $labels.instance }}) or cluster: {{ $labels.cluster }}. Alert sent between 9:00 am and 5:00 pm. Check if certificate is still used and if issuer doesn't report any errors. Most common issuers are: traefik (docker deployments) and cert-manager (kubernetes deployments)"
- name: TLS Certificates expiry
  rules:
    - alert: TLS Certificate expiry
      expr: (probe_ssl_earliest_cert_expiry - time() < 86400 * 20) or (certmanager_certificate_expiration_timestamp_seconds - time() < 86400 * 20)
      for: 30m
      labels:
        severity: page
        job: "{{ $labels.job }}"
        env: "{{ $labels.env }}"
        app: "{{ $labels.app }}"
        cluster: "{{ $labels.cluster }}"
        cluster_env: "{{ $labels.cluster_env }}"
        namespace: "{{ $labels.exported_namespace }}"
      annotations:
        description: "TLS/SSL certificate will expire in {{ $value | humanizeDuration }} (instance: {{ $labels.instance }}) or cluster: {{ $labels.cluster }}. Check if certificate is still used and if issuer doesn't report any errors. Most common issuers are: traefik (docker deployments) and cert-manager (kubernetes deployments)"
