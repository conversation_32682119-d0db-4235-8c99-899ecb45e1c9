#!/bin/bash
alerts1='[
  {
    "labels": {
       "alertname": "TEST - AndrzejProximityAlert!",
       "dev": "sda1",
       "react": "no",
       "instance": "example1",
       "env": "shitty"
     },
     "annotations": {
        "info": "The disk sda1 is running full",
        "summary": "please check the instance example1"
      }
  },
  {
    "labels": {
       "alertname": "TEST - DiskRunningFull",
       "dev": "sda2",
       "react": "no",
       "instance": "example1",
        "env": "shitty"
     },
     "annotations": {
        "info": "The disk sda2 is running full",
        "summary": "please check the instance example1",
        "runbook": "the following link http://test-url should be clickable"
      }
  },
  {
    "labels": {
       "alertname": "TEST - DiskRunningFull",
       "dev": "sda1",
       "react": "no",
       "instance": "example2",
        "env": "shitty"
     },
     "annotations": {
        "info": "The disk sda1 is running full",
        "summary": "please check the instance example2"
      }
  },
  {
    "labels": {
       "alertname": "TEST - DiskRunningFull",
       "dev": "sdb2",
       "react": "no",
       "instance": "example2",
        "env": "shitty"
     },
     "annotations": {
        "info": "The disk sdb2 is running full",
        "summary": "please check the instance example2"
      }
  },
  {
    "labels": {
       "alertname": "TEST - DiskRunningFull",
       "dev": "sda1",
       "react": "no",
       "instance": "example3",
       "severity": "critical",
        "env": "shitty"
     }
  },
  {
    "labels": {
       "alertname": "TEST - DiskRunningFull",
       "dev": "sda1",
       "react": "no",
       "instance": "example3",
       "severity": "page",
       "env": "shitty"
     }
  }
]'
curl -XPOST -d"$alerts1" http://172.16.253.28:9093/api/v2/alerts
