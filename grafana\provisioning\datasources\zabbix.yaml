# config file version
apiVersion: 1
deleteDatasources:
  - name: zabbix_2
    orgId: 1
datasources:
  - name: zabbix_2
    type: alexanderzobnin-zabbix-datasource
    typeLogoUrl: public/plugins/alexanderzobnin-zabbix-datasource/img/zabbix_app_logo.svg
    access: proxy
    orgId: 1
    url: https://int-zabbix.scalepoint.tech/api_jsonrpc.php
    basicAuth: false
    jsonData:
      addThresholds: true
      alerting: true
      alertingMinSeverity: "3"
      disableReadOnlyUsersAck: true
     # apiToken: "c327e33e450f4ab4e830fde15b8ccc2bcbfc5bcd4201097e2fd436448f6678d4"
      tlsAuth: false
      tlsAuthWithCACert: false
      tlsSkipVerify: true
      trends: true
      trendsFrom: "31d"
      trendsRange: "8d"
      zabbixVersion: "4"
    secureJsonData:
      apiToken: "c327e33e450f4ab4e830fde15b8ccc2bcbfc5bcd4201097e2fd436448f6678d4"
    isDefault: false
    version: 1
    editable: true
