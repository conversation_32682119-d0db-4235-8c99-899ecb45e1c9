{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "This dashboard is useful for analysis of CPU overcommitment of Hyper-V hosts", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 78, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "Currently unscientifically divided by 12, but likely exporter needs to be updated to return _Base counter value.\n\nAlert thresholds are set to 60µs and 100µs according to https://helpcenter.veeam.com/docs/one/monitor/hyperv_alarms_events.html", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMax": 75000, "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "dashed"}}, "links": [{"targetBlank": true, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://helpcenter.veeam.com/docs/one/monitor/hyperv_alarms_events.html"}], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 60000}, {"color": "red", "value": 100000}]}, "unit": "ns"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 0}, "id": 1, "interval": "5m", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "avg(rate(windows_hyperv_host_cpu_wait_time_per_dispatch_total{instance=~\"$instance\"}[$__rate_interval])) by (instance) / 12", "instant": false, "interval": "", "legendFormat": "{{label_name}}", "range": true, "refId": "A"}], "title": "Host average CPU Wait time per dispatch", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "Alert thresholds are set to 60µs and 100µs according to https://helpcenter.veeam.com/docs/one/monitor/hyperv_alarms_events.html", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMax": 75000, "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "dashed"}}, "mappings": [], "noValue": "No data is shown when there are too many VMs matching filter. Choose specific physical hosts (instances) or VMs in the filter to see results.", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 60000}, {"color": "red", "value": 100000}]}, "unit": "ns"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 0}, "id": 2, "interval": "5m", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "avg(rate(windows_hyperv_vm_cpu_wait_time_per_dispatch_total{instance=~\"$instance\", vm=~\"$vm\"}[$__rate_interval])) by (vm, instance) / 12", "instant": false, "interval": "", "legendFormat": "{{vm}}@{{instance}}", "range": true, "refId": "A"}], "title": "VM Average CPU Wait time per dispatch", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "Thresholds are set as yellow above 1 and red above 2", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 3}, {"color": "red", "value": 4}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 7}, "id": 8, "interval": "5m", "options": {"displayMode": "lcd", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "exemplar": false, "expr": "sort_desc(count(windows_hyperv_vm_cpu_total_run_time{instance=~\"$instance\"}) by (instance) / max(windows_hyperv_hypervisor_logical_processors{instance=~\"$instance\"}) by (instance))", "instant": true, "interval": "", "legendFormat": "{{instance}}", "range": false, "refId": "A"}], "title": "Host vCPU to logical processors ratio", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "Shows percentage of CPU resources used by each VM", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 50, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "dashed"}}, "links": [{"targetBlank": true, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://helpcenter.veeam.com/docs/one/monitor/hyperv_alarms_events.html"}], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 7}, "id": 4, "interval": "2m", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "(sum (rate(windows_hyperv_vm_cpu_hypervisor_run_time{instance=~\"$instance\"}[$__rate_interval])) by (vm, instance)) / ignoring(vm, instance) group_left max (windows_hyperv_hypervisor_logical_processors{instance=~\"$instance\"}) / 1000", "instant": false, "interval": "", "legendFormat": "{{vm}}@{{instance}}", "range": true, "refId": "A"}], "title": "Host average load by VM (draft - ignore!)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "Alert thresholds are set to 60µs and 100µs according to https://helpcenter.veeam.com/docs/one/monitor/hyperv_alarms_events.html", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "dashed"}}, "links": [{"targetBlank": true, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://helpcenter.veeam.com/docs/one/monitor/hyperv_alarms_events.html"}], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 60000}, {"color": "red", "value": 100000}]}, "unit": "ns"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "id": 6, "interval": "5m", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "rate(windows_hyperv_host_cpu_wait_time_per_dispatch_total{instance=~\"$instance\"}[$__rate_interval]) / 12", "instant": false, "interval": "", "legendFormat": "{{instance}}:{{core}}", "range": true, "refId": "A"}], "title": "Host CPU Wait time per dispatch per core", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "Alert thresholds are set to 60µs and 100µs according to https://helpcenter.veeam.com/docs/one/monitor/hyperv_alarms_events.html", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "dashed"}}, "mappings": [], "noValue": "No data is shown when there are too many VMs matching filter. Choose specific physical hosts (instances) or VMs in the filter to see results.", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 60000}, {"color": "red", "value": 100000}]}, "unit": "ns"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "id": 7, "interval": "5m", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "rate(windows_hyperv_vm_cpu_wait_time_per_dispatch_total{instance=~\"$instance\", vm=~\"$vm\"}[$__rate_interval]) / 12", "instant": false, "interval": "", "legendFormat": "{{vm}}:{{core}}@{{instance}}", "range": true, "refId": "A"}], "title": "VM Average CPU Wait time per dispatch per core", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "Alert thresholds are set to 60µs and 100µs according to https://helpcenter.veeam.com/docs/one/monitor/hyperv_alarms_events.html", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "dashed"}}, "links": [{"targetBlank": true, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://helpcenter.veeam.com/docs/one/monitor/hyperv_alarms_events.html"}], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 22}, "id": 3, "interval": "5m", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "avg(rate(windows_cpu_processor_utility_total{instance=~\"$instance\"}[$__rate_interval]) / rate(windows_cpu_processor_rtc_total{instance=~\"$instance\"}[$__rate_interval])) by (instance)", "instant": false, "interval": "", "legendFormat": "{{label_name}}", "range": true, "refId": "A"}], "title": "Host average CPU Utilization", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "Shows percentage of CPU used by the host itself", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "dashed"}}, "links": [{"targetBlank": true, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://helpcenter.veeam.com/docs/one/monitor/hyperv_alarms_events.html"}], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 22}, "id": 5, "interval": "5m", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "(sum (rate(windows_hyperv_host_cpu_total_run_time{instance=~\"$instance\"}[2m])) by (instance)) / sum by (instance)(windows_cs_logical_processors{}) / 100000", "instant": false, "interval": "", "legendFormat": "{{label_name}}", "range": true, "refId": "A"}], "title": "Host average load (excluding VMs)", "type": "timeseries"}], "preload": false, "refresh": "5m", "schemaVersion": 41, "tags": [], "templating": {"list": [{"current": {"text": ["nghw-clu01"], "value": ["nghw-clu01"]}, "definition": "label_values(windows_hyperv_host_cpu_wait_time_per_dispatch_total,cluster)", "includeAll": true, "label": "Cluster", "multi": true, "name": "cluster", "options": [], "query": {"query": "label_values(windows_hyperv_host_cpu_wait_time_per_dispatch_total,cluster)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": ["All"], "value": ["$__all"]}, "definition": "label_values(windows_hyperv_host_cpu_wait_time_per_dispatch_total{cluster=~\"$cluster\"},instance)", "description": "Physical host name", "includeAll": true, "label": "Host", "multi": true, "name": "instance", "options": [], "query": {"query": "label_values(windows_hyperv_host_cpu_wait_time_per_dispatch_total{cluster=~\"$cluster\"},instance)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": ["All"], "value": ["$__all"]}, "definition": "label_values(windows_hyperv_vm_cpu_wait_time_per_dispatch_total{instance=~\"$instance\"},vm)", "description": "Virtual machine name", "includeAll": true, "label": "VM", "multi": true, "name": "vm", "options": [], "query": {"query": "label_values(windows_hyperv_vm_cpu_wait_time_per_dispatch_total{instance=~\"$instance\"},vm)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Hyper-V CPU Starvation", "uid": "d6f56ab3-961e-4ccd-bbe1-4ea7cb08bc40", "version": 1}