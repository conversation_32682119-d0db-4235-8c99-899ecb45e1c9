services:
  traefik:
    image: traefik:${TRAEFIK_TAG}
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8083:8083"
      - "9091:9091"
      - "9092:9092"
    networks:
      - front
      - back
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /var/run/docker.sock:/var/run/docker.sock
      - ./traefik/etc/traefik.yml:/traefik.yml:z
      - traefik_logs:/var/log/traefik
      - traefik_acme:/etc/traefik/acme
    labels:
      traefik.enable: "true"
      traefik.http.middlewares.traefik-auth.basicauth.users: admin:$$apr1$$LopARXKS$$KmYZaWlG3agGUOEh/Sxsf1
      traefik.http.routers.traefik-secure.entrypoints: https
   #   traefik.http.routers.traefik-secure.rule: Host(`$TRAEFIK_TECH_HOSTNAME`,`localhost`)
      traefik.http.routers.traefik-secure.rule: Host(`$TRAEFIK_TECH_HOSTNAME`) || Host(`localhost`)
      traefik.http.routers.traefik-secure.middlewares: traefik-auth
      traefik.http.routers.traefik-secure.tls: "true"
      traefik.http.routers.traefik-secure.tls.domains[0].main: ${TRAEFIK_TECH_HOSTNAME}
      traefik.http.routers.traefik-secure.tls.certresolver: letsencrypt
      traefik.http.routers.traefik-secure.service: api@internal
      traefik.http.services.traefik.loadbalancer.server.port: 8080
      traefik.http.routers.metrics.rule: PathPrefix(`/metrics`)
      traefik.http.routers.metrics.service: prometheus@internal
      traefik.http.routers.metrics.entrypoints: metrics
      traefik.http.routers.metrics.middlewares: metrics-auth
      traefik.http.middlewares.metrics-auth.basicauth.users: admin:$$apr1$$LopARXKS$$KmYZaWlG3agGUOEh/Sxsf1
    env_file: ./traefik/etc/cloudflare.env

  azure-secrets-exporter:
    build: ./azure-exporter  # Build lokalnie
    container_name: azure-secrets-exporter
    restart: unless-stopped
    ports:
    - "9912:9912"
    environment:
      - AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
      - AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}
      - AZURE_TENANT_ID=${AZURE_TENANT_ID}
    networks:
      - back
    labels:
      traefik.enable: "false"
  renderer:
      image: grafana/grafana-image-renderer:latest
      ports:
        - 8081
      environment:
        ENABLE_METRICS: 'true'
      networks:
        - back
  sql_exporter:
    image: burningalchemist/sql_exporter:latest
    container_name: sql_exporter
    volumes:
      - ./sql_exporter/sql_exporter.yml:/etc/sql_exporter/sql_exporter.yml:z
      - ./sql_exporter/mssql.collector.yml:/etc/sql_exporter/mssql.collector.yml:z
    command:
      - '--config.file=/etc/sql_exporter/sql_exporter.yml'
    ports:
      - "9399:9399"
    networks:
      - back
  grafana:
    restart: unless-stopped
    container_name: grafana
    image: scalepoint/grafana-custom:${GRAFANA_TAG}
    expose:
     - "3000"
    volumes:
      - grafana_lib:/var/lib/grafana
      - grafana_log:/var/log/grafana
      - grafana_etc:/etc/grafana
      - ./grafana/ldap.toml:/etc/grafana/ldap.toml:z
      - ./grafana/provisioning:/etc/grafana/provisioning:z
      # sso
    environment:
      GF_RENDERING_SERVER_URL: http://renderer:8081/render
      GF_RENDERING_CALLBACK_URL: http://${GRAFANA_TECH_HOSTNAME}:3000/
      GF_LOG_FILTERS: rendering:debug
      GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS: grafana-image-renderer
     # GF_LOG_LEVEL: debug
      #SSO
      GF_AUTH_AZUREAD_ENABLED: "true"
      GF_SERVER_ROOT_URL: "https://grafana.scalepoint.tech"
      GF_AUTH_AZUREAD_NAME: "Azure AD"
      GF_AUTH_AZUREAD_CLIENT_ID: "d34eebde-4368-4ea4-9178-b01e48b1004e"
      GF_AUTH_AZUREAD_CLIENT_SECRET: "PLACEHOLDER_GF_AUTH_AZUREAD_CLIENT_SECRET"
      GF_AUTH_AZUREAD_SCOPES: "openid email profile"
      GF_AUTH_AZUREAD_AUTH_URL: "https://login.microsoftonline.com/b59a8795-364b-44c6-92ff-95342a294254/oauth2/v2.0/authorize"
      GF_AUTH_AZUREAD_TOKEN_URL: "https://login.microsoftonline.com/b59a8795-364b-44c6-92ff-95342a294254/oauth2/v2.0/token"
    networks:
      - back
    depends_on:
      - prometheus
      - traefik
    labels:
      traefik.enable: "true"
      traefik.http.routers.grafana-xxl-secure.entrypoints: https
      traefik.http.routers.grafana-xxl-secure.rule: Host(`${GRAFANA_TECH_HOSTNAME}`) || Host(`${GRAFANA_TECH_HOSTNAMESEC}`)
      traefik.http.routers.grafana-xxl-secure.middlewares: hsts-header@file
      traefik.http.routers.grafana-xxl-secure.tls: "true"
      traefik.http.routers.grafana-xxl-secure.tls.domains[0].main: ${GRAFANA_TECH_HOSTNAME}
      traefik.http.routers.grafana-xxl-secure.tls.certresolver: letsencrypt
      traefik.http.routers.grafana-xxl-secure.service: grafana-xxl
      traefik.http.services.grafana-xxl.loadbalancer.server.port: 3000
      traefik.http.services.grafana-xxl.loadbalancer.healthcheck.path: /
      traefik.http.services.grafana-xxl.loadbalancer.healthcheck.interval: 10s
      traefik.http.services.grafana-xxl.loadbalancer.healthcheck.timeout: 5s
    env_file: ./grafana/grafana.env
  prometheus:
    image: prom/prometheus:${PROMETHEUS_TAG}
    container_name: prometheus
    volumes:
      - ./prometheus/:/etc/prometheus/:z
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=90d'
      - '--storage.tsdb.wal-compression'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.external-url=https://int-prometheus.scalepoint.tech'
    expose:
      - "9090"
    networks:
      - back
    restart: unless-stopped
    labels:
      traefik.enable: "true"
      traefik.http.routers.prometheus-secure.entrypoints: https
      traefik.http.routers.prometheus-secure.rule: Host(`${PROMETHEUS_TECH_HOSTNAME}`)
      traefik.http.routers.prometheus-secure.middlewares: hsts-header@file
      traefik.http.routers.prometheus-secure.tls: "true"
      traefik.http.routers.prometheus-secure.tls.domains[0].main: ${PROMETHEUS_TECH_HOSTNAME}
      traefik.http.routers.prometheus-secure.tls.certresolver: letsencrypt
      traefik.http.routers.prometheus-secure.service: prometheus
      traefik.http.services.prometheus.loadbalancer.server.port: 9090
    depends_on:
      - traefik
      - consul
    environment:
      - 'DEBUG=1'
    healthcheck:
      interval: 30s
      timeout: 3s
      start_period: 60s
      test: ["CMD", "wget", "-q", "--spider", "--tries=1", "localhost:9090", "-O /dev/null"]
  # pushgateway:
  #   image: prom/pushgateway:${PUSHGATEWAY_TAG}
  #   container_name: pushgateway
  #   expose:
  #     - '9091'
  #   networks:
  #     - back
  #   restart: unless-stopped
  #   labels:
  #     traefik.enable: "true"
  #     traefik.http.routers.push-gateway-secure.entrypoints: https-9091
  #     traefik.http.routers.push-gateway-secure.rule: Host(`${PROMETHEUS_TECH_HOSTNAME}`)
  #     traefik.http.routers.push-gateway-secure.tls: "true"
  #     traefik.http.routers.push-gateway-secure.service: push-gateway
  #     traefik.http.services.push-gateway.loadbalancer.server.port: 9091
  #   depends_on:
  #     - prometheus
  #     - traefik
  alertmanager:
    image: prom/alertmanager:${ALERTMANAGER_TAG}
    container_name: alertmanager
    expose:
      - "9093"
    volumes:
      - ./prometheus/:/etc/prometheus/:z
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/prometheus/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--log.level=debug'
      - '--web.external-url=https://int-prometheus.scalepoint.tech/'
    restart: unless-stopped
    networks:
      - back
    depends_on:
      - prometheus
      - traefik
    labels:
      traefik.enable: "true"
      traefik.http.routers.alertmanager-secure.entrypoints: https
      traefik.http.routers.alertmanager-secure.rule: Host(`${PROMETHEUS_TECH_HOSTNAME}`) && PathPrefix(`/alertmanager`)
      traefik.http.routers.alertmanager-secure.tls: "true"
      traefik.http.routers.alertmanager-secure.service: alertmanager
      traefik.http.routers.alertmanager-secure.middlewares: alertmanager-strip-prefix,hsts-header@file
      traefik.http.middlewares.alertmanager-strip-prefix.stripprefix.prefixes: /alertmanager
      traefik.http.services.alertmanager.loadbalancer.server.port: 9093
  prom2teams:
    image: idealista/prom2teams:${PROM2TEAMS_TAG}
    container_name: prom2teams
    restart: unless-stopped
    volumes:
      - "./prom2teams/config.ini:/opt/prom2teams/config.ini:z"
      - "./prom2teams/template1.j2:/opt/prom2teams/template1.j2:z"
    networks:
      - back
    expose:
      - "8089"
    depends_on:
      - prometheus
      - alertmanager
    labels:
      traefik.enable: "false"
  consul:
    image: consul:${CONSUL_TAG}
    restart: unless-stopped
    container_name: consul
    expose:
      - "8500"
      - "8300"
    volumes:
      - ./consul/config:/config:z
      - consul_data:/data
    command: agent -server -data-dir=/data -config-dir=/config -bind 0.0.0.0 -client 0.0.0.0 -bootstrap-expect=1 -ui
    labels:
      - "traefik.enable=true"
      - "traefik.http.middlewares.consul-auth.basicauth.users=admin:$$apr1$$LopARXKS$$KmYZaWlG3agGUOEh/Sxsf1"
      - "traefik.http.routers.consul-secure.entrypoints=https"
      - "traefik.http.routers.consul-secure.rule=Host(`${CONSUL_TECH_HOSTNAME}`)"
      - "traefik.http.routers.consul-secure.tls=true"
      - "traefik.http.routers.consul-secure.tls.domains[0].main=${CONSUL_TECH_HOSTNAME}"
      - "traefik.http.routers.consul-secure.tls.certresolver=letsencrypt"
      - "traefik.http.routers.consul-secure.middlewares=consul-auth"
      - "traefik.http.routers.consul-secure.service=consul"
      - "traefik.http.services.consul.loadbalancer.server.port=8500"
    networks:
      - back
  blackbox_exporter:
    image: prom/blackbox-exporter:${BLACKBOX_TAG}
    container_name: blackbox
    expose:
      - "9115"
    restart: unless-stopped
    volumes:
      - "./blackbox:/blackbox:z"
    command:
      - '--config.file=/blackbox/blackbox.yml'
      - '--log.level=debug'
    networks:
      - back
    labels:
      traefik.enable: "false"
# Node exporter
  node-exporter:
    image: prom/node-exporter:${NODE_TAG}
    container_name: node_exporter
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - --collector.filesystem.ignored-mount-points
      - "^/(sys|proc|dev|host|etc|rootfs/var/lib/docker/containers|rootfs/var/lib/docker/overlay2|rootfs/run/docker/netns|rootfs/var/lib/docker/aufs)($$|/)"
    expose:
      - 9100
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
  # HAProxy
  prod-haproxy03:
    image: prom/haproxy-exporter:${HAPROXY_TAG}
    container_name: prod-haproxy03
    command:
      - '--haproxy.scrape-uri=http://readonly:<EMAIL>:1936/haproxy?stats;csv'
      - '--web.listen-address=0.0.0.0:9101'
    expose:
     - 9101
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
  prod-haproxy04:
    image: prom/haproxy-exporter:${HAPROXY_TAG}
    container_name: prod-haproxy04
    command:
      - '--haproxy.scrape-uri=http://readonly:<EMAIL>:1936/haproxy?stats;csv'
      - '--web.listen-address=0.0.0.0:9101'
    expose:
     - 9101
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
  prod-haproxy05:
    image: prom/haproxy-exporter:${HAPROXY_TAG}
    container_name: prod-haproxy05
    command:
      - '--haproxy.scrape-uri=http://readonly:<EMAIL>:1936/haproxy?stats;csv'
      - '--web.listen-address=0.0.0.0:9101'
    expose:
     - 9101
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
  prod-haproxy06:
    image: prom/haproxy-exporter:${HAPROXY_TAG}
    container_name: prod-haproxy06
    command:
      - '--haproxy.scrape-uri=http://readonly:<EMAIL>:1936/haproxy?stats;csv'
      - '--web.listen-address=0.0.0.0:9101'
    expose:
     - 9101
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
  int-haproxy03:
    image: prom/haproxy-exporter:${HAPROXY_TAG}
    container_name: int-haproxy03
    command:
      - '--haproxy.scrape-uri=http://readonly:aeLai5Aice1i@**********:1936/haproxy?stats;csv'
      - '--web.listen-address=0.0.0.0:9101'
    expose:
     - 9101
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
  int-haproxy04:
    image: prom/haproxy-exporter:${HAPROXY_TAG}
    container_name: int-haproxy04
    command:
      - '--haproxy.scrape-uri=http://readonly:aeLai5Aice1i@**********:1936/haproxy?stats;csv'
      - '--web.listen-address=0.0.0.0:9101'
    expose:
     - 9101
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
# Elasticsearch
  pjt-ecb-el01:
    image: justwatch/elasticsearch_exporter:${ELASTICSEARCH_TAG}
    container_name: pjt-ecb-el01
    command:
     - '--es.uri=https://pjt-ecb-el01.spcph.local'
     - '--es.all'
     - '--es.indices'
     - '--es.cluster_settings'
     - '--es.indices_settings'
     - '--es.shards'
     - '--es.timeout=20s'
     - '--es.ssl-skip-verify'
    restart: unless-stopped
    expose:
    - "9144"
    networks:
      - back
    depends_on:
      - prometheus
    labels:
      traefik.enable: "false"
  test-ecb-el-c1:
    container_name: test-ecb-el-c1
    image: justwatch/elasticsearch_exporter:${ELASTICSEARCH_TAG}
    command:
     - '--es.uri=https://ito:<EMAIL>:9200'
     - '--es.all'
     - '--es.indices'
     - '--es.cluster_settings'
     - '--es.indices_settings'
     - '--es.shards'
     - '--es.timeout=20s'
     - '--es.ssl-skip-verify'
    restart: unless-stopped
    expose:
    - "9114"
    networks:
      - back
    depends_on:
      - prometheus
    labels:
      traefik.enable: "false"
  prod-ecb-el-c1:
    image: justwatch/elasticsearch_exporter:${ELASTICSEARCH_TAG}
    container_name: prod-ecb-el-c1
    command:
     - '--es.uri=https://ito:<EMAIL>:9200'
     - '--es.all'
     - '--es.indices'
     - '--es.cluster_settings'
     - '--es.indices_settings'
     - '--es.shards'
     - '--es.timeout=20s'
     - '--es.ssl-skip-verify'
    restart: unless-stopped
    expose:
    - "9114"
    networks:
      - back
    depends_on:
      - prometheus
    labels:
      traefik.enable: "false"
# Graylog
  ito-graylog:
    image: justwatch/elasticsearch_exporter:${ELASTICSEARCH_TAG}
    container_name: ito-graylog
    command:
     - '--es.uri=http://ito-graylog01.spcph.local:9200'
     - '--es.all'
     - '--es.indices'
     - '--es.cluster_settings'
     - '--es.indices_settings'
     - '--es.shards'
     - '--es.timeout=20s'
     - '--es.ssl-skip-verify'
    restart: unless-stopped
    expose:
    - "9114"
    networks:
      - back
    labels:
      traefik.enable: "false"
  dev-graylog:
    image: justwatch/elasticsearch_exporter:${ELASTICSEARCH_TAG}
    container_name: dev-graylog
    command:
     - '--es.all'
     - '--es.indices'
     - '--es.cluster_settings'
     - '--es.indices_settings'
     - '--es.shards'
     - '--es.timeout=20s'
     - '--es.ssl-skip-verify'
     - '--es.uri=http://dev-graylog1.spcph.local:9200'
    restart: unless-stopped
    expose:
    - "9114"
    networks:
      - back
    labels:
      traefik.enable: "false"
  prod-graylog:
    image: justwatch/elasticsearch_exporter:${ELASTICSEARCH_TAG}
    container_name: prod-graylog
    command:
     - '--es.uri=http://prod-graylog1.scalepoint.lan:9200'
     - '--es.all'
     - '--es.indices'
     - '--es.cluster_settings'
     - '--es.indices_settings'
     - '--es.shards'
     - '--es.timeout=20s'
     - '--es.ssl-skip-verify'
    restart: unless-stopped
    expose:
    - "9114"
    networks:
      - back
    labels:
      traefik.enable: "false"
# redis
  easyclaims-sandbox-redis:
    image: oliver006/redis_exporter:${REDIS_TAG}
    container_name: seasyclaims-sandbox-redis
    expose:
    - "9121"
    command:
    - '-redis.addr=rediss://easyclaims-sandbox-redis.scalepoint.work:443'
    - '-redis.password=SBFYPB5f43'
    - '-include-system-metrics'
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
  easyclaims-prod-redis:
    image: oliver006/redis_exporter:${REDIS_TAG}
    container_name: easyclaims-prod-redis
    expose:
    - "9121"
    command:
    - '-redis.addr=rediss://easyclaims-prod-redis.scalepoint.work:443'
    - '-redis.password=4mxoCMBu52'
    - '-include-system-metrics'
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"

# rabbitmq
#  prod-shr-mq:
#    image: kbudde/rabbitmq-exporter:${RABBITMQ_TAG}
#    container_name: prod-shr-mq
#    expose:
#    - '9149'
#    environment:
#      PUBLISH_PORT: '9149'
#      RABBIT_URL: 'http://prod-shr-mq.scalepoint.work'
#      RABBIT_USER: admin
#      RABBIT_PASSWORD: instructnotably1importance
#      SKIPVERIFY: 'true'
#    restart: unless-stopped
#    networks:
#      - back
#    labels:
#      traefik.enable: "false"
  foreman_exporter:
    container_name: foreman_exporter
    image: marcinbojko/foreman_exporter:${FOREMAN_EXPORTER_TAG}
    expose:
     - "8000"
    env_file:
      ./foreman_exporter/foreman_exporter.env
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
  ofelia:
    image: mcuadros/ofelia:${OFELIA_TAG}
    restart: unless-stopped
    container_name: ofelia
    volumes:
      - ./ofelia/config.ini:/etc/config.ini:z
    command:
      - 'daemon'
      - '--config=/etc/config.ini'
    networks:
      - back
    labels:
      traefik.enable: "false"
  filebeat:
    image: elastic/filebeat:${FILEBEAT_TAG}
    container_name: filebeat
    user: root
    restart: unless-stopped
    # Disabling strict permission check:
    # https://www.elastic.co/guide/en/beats/libbeat/6.6/config-file-permissions.html
    command: ["--strict.perms=false"]
    volumes:
     - traefik_logs:/var/log/traefik:ro
     - filebeat_registry:/usr/share/filebeat/data
     - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro,z
    expose:
      - "5601"
    networks:
      - back
    labels:
      traefik.enable: "false"
    env_file:
      - ./.env
  bigip1:
    image: expressenab/bigip_exporter:${BIGIP_TAG}
    container_name: bigip1
    command:
      - '--bigip.host=big-ip1.spcph.local'
      - '--bigip.port=443'
      - '--bigip.username=prometheus'
      - '--bigip.password=Woi1ail9guxuap1OhC0u'
      - '--exporter.bind_address=0.0.0.0'
      - '--exporter.namespace=bigip'
      - '--exporter.bind_port=9143'
    expose:
     - 9143
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
  bigip2:
    image: expressenab/bigip_exporter:${BIGIP_TAG}
    container_name: bigip2
    command:
      - '--bigip.host=big-ip2.spcph.local'
      - '--bigip.port=443'
      - '--bigip.username=prometheus'
      - '--bigip.password=Woi1ail9guxuap1OhC0u'
      - '--exporter.bind_address=0.0.0.0'
      - '--exporter.namespace=bigip'
      - '--exporter.bind_port=9144'
    expose:
     - 9144
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
  bigip3:
    image: expressenab/bigip_exporter:${BIGIP_TAG}
    container_name: bigip3
    command:
      - '--bigip.host=big-ip3.spcph.local'
      - '--bigip.port=443'
      - '--bigip.username=prometheus'
      - '--bigip.password=Woi1ail9guxuap1OhC0u'
      - '--exporter.bind_address=0.0.0.0'
      - '--exporter.namespace=bigip'
      - '--exporter.bind_port=9145'
    expose:
     - 9145
    restart: unless-stopped
    networks:
      - back
    labels:
      traefik.enable: "false"
  portainer_agent:
    image: portainer/agent:${PORTAINER_AGENT_TAG}
    restart:  unless-stopped
    container_name: portainer_agent
    networks:
      - back
      - front
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /var/lib/docker/volumes:/var/lib/docker/volume
    ports:
      - "9001:9001"
    labels:
      traefik.enable: "false"
# volumes
volumes:
  grafana_lib: {}
  grafana_log: {}
  grafana_etc: {}
  prometheus_data: {}
  prometheus_etc: {}
  alertmanager_data: {}
  traefik_logs: {}
  traefik_acme: {}
  consul_data: {}
  influxdb_data: {}
  chronograf_data: {}
  filebeat_registry: {}
networks:
  front:
    ipam:
      config:
        - subnet: ************/24
  back:
    ipam:
      config:
        - subnet: ************/24
