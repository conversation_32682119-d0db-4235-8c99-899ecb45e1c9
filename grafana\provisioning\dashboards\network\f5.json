{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 58, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 0}, "hiddenSeries": false, "id": 3, "interval": "2m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(bigip_pool_tot_requests{pool=\"${pool}\"}[$__interval]))", "instant": false, "interval": "", "legendFormat": "Current", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(bigip_pool_tot_requests{pool=\"${pool}\"}[$__interval] offset 7d))", "instant": false, "interval": "", "legendFormat": "7 days before", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Requests", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:64", "format": "reqps", "logBase": 1, "show": true}, {"$$hashKey": "object:65", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": ""}, "description": "", "fill": 1, "fillGradient": 5, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 0}, "hiddenSeries": false, "id": 2, "interval": "2m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": ""}, "expr": "sum(rate(bigip_pool_serverside_bytes_in{pool=\"${pool}\"}[$__interval]))", "interval": "", "legendFormat": "Current", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": ""}, "expr": "sum(rate(bigip_pool_serverside_bytes_in{pool=\"${pool}\"}[$__interval] offset 7d))", "interval": "", "legendFormat": "7 days before", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Traffic in", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:64", "format": "binBps", "logBase": 1, "show": true}, {"$$hashKey": "object:65", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fill": 1, "fillGradient": 5, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 0}, "hiddenSeries": false, "id": 6, "interval": "2m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(bigip_pool_serverside_bytes_out{pool=\"${pool}\"}[$__interval]))", "interval": "", "legendFormat": "Current", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(bigip_pool_serverside_bytes_out{pool=\"${pool}\"}[$__interval] offset 7d))", "interval": "", "legendFormat": "7 days before", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Traffic out", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:64", "format": "binBps", "logBase": 1, "show": true}, {"$$hashKey": "object:65", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 7}, "hiddenSeries": false, "id": 5, "interval": "2m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(bigip_pool_serverside_cur_conns{pool=\"${pool}\"})", "interval": "", "legendFormat": "Current", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(bigip_pool_serverside_cur_conns{pool=\"${pool}\"} offset 7d)", "interval": "", "legendFormat": "7 days before", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Current connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:64", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:65", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 7}, "hiddenSeries": false, "id": 14, "interval": "2m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(bigip_pool_serverside_bytes_in{pool=\"${pool}\"}[$__interval]))", "interval": "", "legendFormat": "Bytes In", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(bigip_pool_serverside_bytes_out{pool=\"${pool}\"}[$__interval]))", "interval": "", "legendFormat": "Bytes Out", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Traffic in/out", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:64", "format": "binBps", "logBase": 1, "show": true}, {"$$hashKey": "object:65", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 7}, "hiddenSeries": false, "id": 13, "interval": "2m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(bigip_pool_serverside_pkts_in{pool=\"${pool}\"}[$__interval]))", "interval": "", "legendFormat": "Bytes In", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(bigip_pool_serverside_pkts_out{pool=\"${pool}\"}[$__interval]))", "interval": "", "legendFormat": "Bytes Out", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Packets in/out", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:64", "format": "pps", "logBase": 1, "show": true}, {"$$hashKey": "object:65", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 13}, "hiddenSeries": false, "id": 11, "interval": "2m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "topk(5, sum by (pool) (rate(bigip_pool_tot_requests[15m])))", "instant": false, "interval": "", "legendFormat": "{{pool}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Top 5 Pools by request count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:111", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:112", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 13}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "topk(5, sum by (pool) (rate(bigip_pool_serverside_bytes_in[15m])))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Panel Title", "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 13}, "hiddenSeries": false, "id": 12, "interval": "2m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "topk(5, sum by (pool) (rate(bigip_pool_serverside_bytes_out[15m])))", "instant": false, "interval": "", "legendFormat": "{{pool}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Top 5 Pools by bytes out", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:111", "format": "binBps", "logBase": 1, "show": true}, {"$$hashKey": "object:112", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 19}, "hiddenSeries": false, "id": 7, "interval": "2m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "label_replace(rate(bigip_pool_tot_requests{pool=\"${pool}\"}[$__interval]), \"instance\", \"$1\", \"instance\", \"(.*):.*\")", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Requests by F5 instance", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:64", "format": "reqps", "logBase": 1, "show": true}, {"$$hashKey": "object:65", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Age"}, "properties": [{"id": "unit", "value": "s"}]}]}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 19}, "id": 15, "interval": "2m", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.15", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(bigip_pool_connq_depth{pool=\"${pool}\"})", "interval": "", "legendFormat": "De<PERSON><PERSON>", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(bigip_pool_connq_age_head{pool=\"${pool}\"})", "interval": "", "legendFormat": "Age", "refId": "B"}], "title": "Connection queue", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 19}, "id": 9, "interval": "2m", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.5.15", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "label_replace(bigip_pool_active_member_cnt{pool=\"${pool}\"}, \"instance\", \"$1\", \"instance\", \"(.*):.*\")", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "title": "Active member count", "type": "stat"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": ["loadbalancer", "f5"], "templating": {"list": [{"current": {"selected": false, "text": "ecb--entry.scalepoint.com", "value": "ecb--entry.scalepoint.com"}, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "definition": "label_values(bigip_pool_tot_requests,pool)", "hide": 0, "includeAll": false, "label": "Pool", "multi": false, "name": "pool", "options": [], "query": {"query": "label_values(bigip_pool_tot_requests,pool)", "refId": "prometheus-ito-pool-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-12h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "F5 Pool Metrics", "uid": "qxHvBSl7z", "version": 3, "weekStart": ""}