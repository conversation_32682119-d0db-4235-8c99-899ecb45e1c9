#!/bin/bash

if ! which promtool; then
  echo "No promtool, exiting"
  exit -1
fi

if ! promtool check config ./prometheus/prometheus.yml; then
  echo "Checking config failed, exiting"
  exit -1
fi

ssh <EMAIL> "cd /srv/prometheus&& docker-compose down --remove-orphans||true"
ssh <EMAIL> "rm -rf /srv/prometheus.old||true"
ssh <EMAIL> "mv /srv/prometheus /srv/prometheus.old||true"
ssh <EMAIL> "mkdir -p /srv/prometheus"
#scp -r ./* <EMAIL>:/srv/prometheus
rsync -Pazv -r --exclude-from=.exclude.lst --delete-after --delete-excluded ./ <EMAIL>:/srv/prometheus
ssh <EMAIL> "chmod 777 -R /var/lib/docker/volumes/prometheus_consul_data"
ssh <EMAIL> "cp -pfv /srv/prometheus/prom2teams/config.prod /srv/prometheus/prom2teams/config.ini"
ssh <EMAIL> "cp -pfv /srv/prometheus/traefik/etc/cloudflare.env.prod /srv/prometheus/traefik/etc/cloudflare.env"
#ssh <EMAIL> "mv -vf /var/lib/docker/volumes/prometheus_grafana_lib/_data/grafana.db /var/lib/docker/volumes/prometheus_grafana_lib/_data/grafana.old"
ssh <EMAIL> "rm -rf /srv/prometheus/alertmanager/*"
ssh <EMAIL> "cd /srv/prometheus&& docker-compose pull"
ssh <EMAIL> "cd /srv/prometheus&& docker-compose up -d --build "
ssh <EMAIL> "docker image prune --all -f"
