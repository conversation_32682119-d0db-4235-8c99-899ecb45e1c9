  # Haproxy
groups:
  - name: HAproxy related alerts
    rules:
    # Generic haproxy instances
    - alert: HAProxy - one of nodes is not available.
      expr: haproxy_up < 1
      for: 5m
      labels:
        severity: page
        job: "{{ $labels.job }}"
        env: "{{ $labels.env }}"
      annotations:
        summary: 'HAProxy - one of nodes is not available: {{ $labels.instance }}.'
    - alert: HAProxy - less than 2 working nodes.
      expr: sum (haproxy_up) by (job) < 2
      for: 5m
      labels:
        severity: page
        job: "{{ $labels.job }}"
        env: "{{ $labels.env }}"
      annotations:
        summary: 'HAProxy - less than 2 working nodes.: {{ $labels.instance }}.'
    # ENV=PROD
    - alert: HAProxy - one of backends PROD is not available
      expr: |
        (
          haproxy_backend_up{job="prod-haproxy",backend!~"(stats|tst-|test-).*",backend!~"sandbox.*"} < 1
        )
        or
        (
          haproxy_backend_up{job="prod-haproxy",backend=~"sandbox.*"} < 1
          and
          (hour() < 21 or hour() >= 6)
        )
      for: 15m
      labels:
        severity: page
        job: "{{ $labels.job }}"
        env: "{{ $labels.env }}"
      annotations:
        summary: 'HAProxy - one of backends is not available {{ $labels.backend }}. Check: http://prod-haproxy03.scalepoint.tech:1936/haproxy?stats, http://prod-haproxy04.scalepoint.tech:1936/haproxy?stats, http://prod-haproxy05.scalepoint.tech:1936/haproxy?stats, http://prod-haproxy06.scalepoint.tech:1936/haproxy?stats'
    # ENV=PROD-ITO-HPRX
    # ENV=INT
    - alert: HAProxy - one of backends DEV/QA/INTERNAL is not available
      expr: haproxy_backend_up {job="int-haproxy",backend!~"(stats|qa-|stg-|stage-).*"} < 1
      for: 5m
      labels:
        severity: page
        job: "{{ $labels.job }}"
        env: "{{ $labels.env }}"
      annotations:
        summary: 'HAProxy - one of backends is not available {{ $labels.backend }}. Check: http://int-haproxy03.scalepoint.tech:1936/haproxy?stats, http://int-haproxy04.scalepoint.tech:1936/haproxy?stats'
