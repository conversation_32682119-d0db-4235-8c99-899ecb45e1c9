{"annotations": {"list": [{"$$hashKey": "object:257", "builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "HAProxy with Prometheus data", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 12693, "graphTooltip": 1, "id": 86, "links": [{"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "GitHub", "tooltip": "", "type": "link", "url": "https://github.com/rfmoz/grafana-dashboards"}, {"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "<PERSON><PERSON>", "tooltip": "", "type": "link", "url": "https://grafana.com/grafana/dashboards/12693-haproxy-2-full/"}], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "prometheus", "uid": "000000001"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 152, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "000000001"}, "refId": "A"}], "title": "Basic General Info", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "- back / + front", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Back.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}, {"matcher": {"id": "byRegexp", "options": "/.*1.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#6ED0E0", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*2.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#7EB26D", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*3.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#1F78C1", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*4.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#CCA300", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*5.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#890F02", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*other.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#806EB7", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 1}, "id": 83, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_frontend_http_responses_total{frontend=~\"$frontend\",code=~\"$code\",instance=\"$host\"}[$__rate_interval])) by (code)", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Front {{ code }}", "metric": "", "refId": "A", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_backend_http_responses_total{backend=~\"$backend\",code=~\"$code\",instance=\"$host\"}[$__rate_interval])) by (code)", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Back {{ code }}", "metric": "", "refId": "B", "step": 240}], "title": "All HTTP responses", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "- out / + in", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bits"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*OUT.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 11}, "id": 75, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_frontend_bytes_in_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])*8) by (instance)", "interval": "$interval", "intervalFactor": 1, "legendFormat": "IN Front", "metric": "", "refId": "A", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_frontend_bytes_out_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])*8) by (instance)", "interval": "$interval", "intervalFactor": 2, "legendFormat": "OUT Front", "refId": "B", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_backend_bytes_in_total{backend=~\"$backend\",instance=\"$host\"}[$__rate_interval])*8) by (instance)", "intervalFactor": 2, "legendFormat": "IN Back", "refId": "C", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_backend_bytes_out_total{backend=~\"$backend\",instance=\"$host\"}[$__rate_interval])*8) by (instance)", "intervalFactor": 2, "legendFormat": "OUT Back", "refId": "D", "step": 240}], "title": "Incoming / Outgoing bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "- back / + front", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Back.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}, {"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 11}, "id": 79, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "sum(rate(haproxy_frontend_sessions_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])) by (instance)", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Front", "metric": "", "range": true, "refId": "A", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "sum(rate(haproxy_frontend_sessions_total{backend=~\"$backend\",instance=\"$host\"}[$__rate_interval])) by (instance)", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Back errors", "metric": "", "range": true, "refId": "C", "step": 240}], "title": "Number of connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "- back / + front", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Back.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}, {"matcher": {"id": "byRegexp", "options": "/.*errors.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*warn.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#FF9830", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 21}, "id": 81, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_frontend_http_requests_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])) by (instance)", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Front requests", "metric": "", "refId": "A", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_frontend_request_errors_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])) by (instance)", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Front requests errors", "metric": "", "refId": "C", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_frontend_requests_denied_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])) by (instance)", "interval": "$interval", "intervalFactor": 2, "legendFormat": "Front request denied", "refId": "F", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_backend_redispatch_warnings_total{backend=~\"$backend\",instance=\"$host\"}[$__rate_interval])) by (instance)", "interval": "$interval", "intervalFactor": 2, "legendFormat": "Back redispatch warnings", "refId": "D", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_backend_retry_warnings_total{backend=~\"$backend\",instance=\"$host\"}[$__rate_interval])) by (instance)", "interval": "$interval", "intervalFactor": 2, "legendFormat": "Back retry warnings", "refId": "E", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_backend_response_errors_total{backend=~\"$backend\",instance=\"$host\"}[$__rate_interval])) by (instance)", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Back response errors", "metric": "", "refId": "I", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(haproxy_backend_current_queue{backend=~\"$backend\",instance=\"$host\"}) by (instance)", "interval": "$interval", "intervalFactor": 2, "legendFormat": "Back queued requests", "refId": "G", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(rate(haproxy_backend_http_requests_total{backend=~\"$backend\",instance=\"$host\"}[$__rate_interval])) by (instance)", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Back requests", "metric": "", "refId": "H", "step": 240}], "title": "Requests and Responses", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "- back / + front", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Back.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 21}, "id": 84, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(haproxy_frontend_current_sessions{frontend=~\"$frontend\",instance=\"$host\"}) by (instance)", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Front", "metric": "", "refId": "B", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "sum(haproxy_backend_current_sessions{frontend=~\"$frontend\",instance=\"$host\"}) by (instance)", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Back", "metric": "", "refId": "A", "step": 240}], "title": "Active sessions", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "000000001"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 31}, "id": 182, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "000000001"}, "refId": "A"}], "title": "Throughtput / Connections", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "- out / + in", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bits"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*OUT.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 14, "w": 24, "x": 0, "y": 32}, "id": 42, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rate(haproxy_frontend_bytes_in_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])*8", "interval": "$interval", "intervalFactor": 1, "legendFormat": "IN {{ proxy }}", "metric": "", "refId": "A", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rate(haproxy_frontend_bytes_out_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])*8", "interval": "$interval", "intervalFactor": 2, "legendFormat": "OUT {{ proxy }}", "refId": "B", "step": 240}], "title": "Front - Incoming / Outgoing bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "- denied / + successful", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Den<PERSON>*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 15, "w": 12, "x": 0, "y": 46}, "id": 43, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "rate(haproxy_frontend_sessions_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Successful {{ proxy }}", "metric": "haproxy_backe", "range": true, "refId": "B", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "rate(haproxy_frontend_requests_denied_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Denied {{ proxy }}", "metric": "haproxy_backe", "range": true, "refId": "A", "step": 240}], "title": "Front - Connections successful / denied", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "connections", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 15, "w": 12, "x": 12, "y": 46}, "id": 114, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "haproxy_frontend_max_session_rate{frontend=~\"$frontend\",instance=\"$host\"}", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Max {{ proxy }}", "metric": "haproxy_backe", "range": true, "refId": "B", "step": 240}], "title": "Front - Maximum observed number of connections per second", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "000000001"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 61}, "id": 154, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "000000001"}, "refId": "A"}], "title": "Queues", "type": "row"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "000000001"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 62}, "id": 155, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "000000001"}, "refId": "A"}], "title": "Requests / Responses", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "- error - denied / + ok", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Error.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}, {"matcher": {"id": "byRegexp", "options": "/.*Denied.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 14, "w": 12, "x": 0, "y": 63}, "id": 46, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rate(haproxy_frontend_http_requests_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Total {{ proxy }}", "metric": "", "refId": "A", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rate(haproxy_frontend_request_errors_total{frontend=~\"$frontend\", instance=\"$host\"}[$__rate_interval])", "interval": "$interval", "intervalFactor": 2, "legendFormat": "Error {{ proxy }}", "refId": "B", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rate(haproxy_frontend_requests_denied_total{frontend=~\"$frontend\", instance=\"$host\"}[$__rate_interval])", "interval": "$interval", "intervalFactor": 2, "legendFormat": "Denied {{ proxy }}", "refId": "C", "step": 240}], "title": "Front - HTTP requests OK / Error / Denied", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "000000001"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 77}, "id": 176, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "000000001"}, "refId": "A"}], "title": "Times", "type": "row"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "000000001"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 78}, "id": 156, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "000000001"}, "refId": "A"}], "title": "Responses by HTTP code", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "responses", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 13, "w": 12, "x": 0, "y": 79}, "id": 47, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rate(haproxy_frontend_http_responses_total{frontend=~\"$frontend\", code=~\"$code\",instance=\"$host\"}[$__rate_interval])", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{ code }} {{ proxy }} ", "metric": "", "refId": "A", "step": 240}], "title": "Front - HTTP responses code", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "responses", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 13, "w": 12, "x": 12, "y": 79}, "id": 24, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "expr": "rate(haproxy_backend_http_responses_total{backend=~\"$backend\", code=~\"$code\",instance=\"$host\"}[$__rate_interval])", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{ code }} {{ proxy }}", "metric": "", "range": true, "refId": "A", "step": 240}], "title": "Back - HTTP responses code", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "000000001"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 92}, "id": 157, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "000000001"}, "refId": "A"}], "title": "Sessions", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "- denied / + total", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Denied.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 13, "w": 12, "x": 0, "y": 93}, "id": 45, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rate(haproxy_frontend_sessions_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Total {{ proxy }}", "metric": "", "refId": "A", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "rate(haproxy_frontend_denied_sessions_total{frontend=~\"$frontend\",instance=\"$host\"}[$__rate_interval])", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Denied {{ proxy }}", "metric": "", "refId": "B", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "haproxy_frontend_current_sessions{frontend=~\"$frontend\",instance=\"$host\"}", "hide": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Current active {{ proxy }}", "metric": "", "refId": "C", "step": 240}], "title": "Front - Number of sessions", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "sessions", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Limit.*/"}, "properties": [{"id": "custom.fillOpacity", "value": 0}]}]}, "gridPos": {"h": 13, "w": 12, "x": 12, "y": 93}, "id": 51, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "haproxy_frontend_max_sessions{frontend=~\"$frontend\",instance=\"$host\"}", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Max {{ proxy }}", "metric": "", "refId": "A", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "haproxy_frontend_limit_sessions{frontend=~\"$frontend\",instance=\"$host\"}", "interval": "$interval", "intervalFactor": 2, "legendFormat": "Limit {{ proxy }}", "refId": "B", "step": 240}], "title": "Front - Maximum observed number of active sessions and limit", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "sessions", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Limit.*/"}, "properties": [{"id": "custom.fillOpacity", "value": 0}]}]}, "gridPos": {"h": 15, "w": 24, "x": 0, "y": 106}, "id": 69, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "haproxy_frontend_max_session_rate{frontend=~\"$frontend\",instance=\"$host\"}", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Max {{ proxy }}", "metric": "", "refId": "A", "step": 240}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "haproxy_frontend_limit_session_rate{frontend=~\"$frontend\",instance=\"$host\"}", "interval": "", "intervalFactor": 2, "legendFormat": "Limit {{ proxy }}", "refId": "B", "step": 240}], "title": "Front - Maximum observed number of sessions per second and limit", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "000000001"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 121}, "id": 158, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "000000001"}, "refId": "A"}], "title": "Health and Weight", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "weight", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 14, "w": 12, "x": 0, "y": 122}, "id": 39, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "asc"}}, "pluginVersion": "9.2.0", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "expr": "haproxy_backend_weight{backend=~\"$backend\", instance=\"$host\"}", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{ proxy }}", "metric": "", "refId": "A", "step": 240}], "title": "Back - Service weight", "type": "timeseries"}], "refresh": "1m", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": ["haproxy", "servers"], "templating": {"list": [{"current": {"selected": false, "text": "default", "value": "default"}, "hide": 0, "includeAll": false, "label": "datasource", "multi": false, "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": false, "text": "prod-haproxy04:9101", "value": "prod-haproxy04:9101"}, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "definition": "label_values(haproxy_frontend_http_responses_total,instance)", "hide": 0, "includeAll": false, "label": "Host", "multi": false, "name": "host", "options": [], "query": {"query": "label_values(haproxy_frontend_http_responses_total,instance)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query", "useTags": false}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "definition": "label_values(haproxy_backend_server_selected_total{instance=\"$host\"},backend)", "hide": 0, "includeAll": true, "label": "Backend", "multi": true, "name": "backend", "options": [], "query": {"query": "label_values(haproxy_backend_server_selected_total{instance=\"$host\"},backend)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query", "useTags": false}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "definition": "label_values(haproxy_frontend_bytes_in_total{instance=\"$host\"},frontend)", "hide": 0, "includeAll": true, "label": "Frontend", "multi": true, "name": "frontend", "options": [], "query": {"query": "label_values(haproxy_frontend_bytes_in_total{instance=\"$host\"},frontend)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "definition": "", "hide": 0, "includeAll": true, "label": "HTTP Code", "multi": true, "name": "code", "options": [], "query": {"query": "label_values(haproxy_server_http_responses_total{instance=\"$host\"}, code)", "refId": "Prometheus-code-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query", "useTags": false}, {"auto": true, "auto_count": 30, "auto_min": "10s", "current": {"selected": false, "text": "30s", "value": "30s"}, "hide": 0, "label": "Interval", "name": "interval", "options": [{"selected": false, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": true, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "1d", "value": "1d"}], "query": "30s,1m,5m,1h,6h,1d", "refresh": 2, "skipUrlSync": false, "type": "interval"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "HAProxy 2 Full", "uid": "rEqu1u5ue", "version": 8, "weekStart": ""}