{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "PGHW-CL1  - Space utilization", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 0, "links": [], "maxDataPoints": "", "maxPerRow": 12, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": ""}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "/.*ClusterStorage*/"}, "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "dsType": "elasticsearch", "evaltype": "0", "functions": [{"$$hashKey": "object:120", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:146", "added": false, "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/hv-sa-03.scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/hv-sa-03.scalepoint.tech(.*)/, $1)"}, {"$$hashKey": "object:121", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["ClusterStorage", ":"], "text": "replaceAlias(ClusterStorage, :)"}], "group": {"filter": "Hyper-V"}, "host": {"filter": "hv-sa-15.scalepoint.tech"}, "item": {"filter": "/.* Space utilization/"}, "itemTag": {"filter": "/.*ClusterStorage*/"}, "macro": {"filter": ""}, "metrics": [{"id": "1", "type": "count"}], "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "D", "resultFormat": "time_series", "schema": 12, "slaProperty": "sla", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "target": "", "textFilter": "", "timeField": "timestamp", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [{"$$hashKey": "object:147", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 95, "yaxis": "left"}, {"$$hashKey": "object:148", "colorMode": "warning", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}, {"$$hashKey": "object:149", "colorMode": "custom", "fill": true, "fillColor": "#bf1b00", "line": false, "op": "gt", "value": 98, "yaxis": "left"}], "timeRegions": [], "title": "PGHW-CL1 - Space utilization", "tooltip": {"shared": false, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "series", "show": true, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:118", "format": "percent", "label": "Percent of used space", "logBase": 1, "show": true}, {"$$hashKey": "object:119", "format": "percent", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 6}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "HyperV Host"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:303", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["10", "avg"], "text": "top(10, avg)"}, {"$$hashKey": "object:304", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: HyperV Logical CPU Total Runtime %(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: HyperV Logical CPU Total Runtime %(.*)/, $1)"}], "group": {"filter": "ITO-Cluster"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "HyperV Logical CPU Total Runtime %"}, "itemTag": {"filter": "Application: HyperV Host"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "warning", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}, {"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 95}], "timeRegions": [], "title": "CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 6}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Memory"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:196", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.tech: Memory utilization", ":"], "text": "replaceAlias(.scalepoint.tech: Memory utilization, :)"}], "group": {"filter": "ITO-Cluster"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "Memory utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"$$hashKey": "object:276", "colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 10000, "yaxis": "left"}], "timeRegions": [], "title": "Memory utilization", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:246", "format": "percent", "logBase": 1, "show": true}, {"$$hashKey": "object:247", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 11, "w": 10, "x": 0, "y": 13}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "General"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:311", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:312", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: Number of processes(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: Number of processes(.*)/, $1)"}], "group": {"filter": "ITO-Cluster"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "Number of processes"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"$$hashKey": "object:391", "colorMode": "warning", "fill": true, "line": true, "op": "gt", "value": 300, "yaxis": "left"}, {"$$hashKey": "object:392", "colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 400, "yaxis": "left"}], "timeRegions": [], "title": "Number of processes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:363", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:364", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 11, "w": 10, "x": 10, "y": 13}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": "", "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Filesystem C:"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:1857", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: C:: Space utilization(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: C:: Space utilization(.*)/, $1)"}], "group": {"filter": "ITO-Cluster"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "C:: Space utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "G", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"$$hashKey": "object:1485", "colorMode": "critical", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 90, "yaxis": "left"}, {"$$hashKey": "object:1486", "colorMode": "warning", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 80, "yaxis": "left"}, {"$$hashKey": "object:1487", "colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 75, "yaxis": "left"}], "timeRegions": [], "title": "Disk utilization on C:", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1452", "format": "percent", "logBase": 2, "show": true}, {"$$hashKey": "object:1453", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 4, "x": 20, "y": 13}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "HyperV Host"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["9", "avg"], "text": "top(9, avg)"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: HyperV VMs Critical(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: HyperV VMs Critical(.*)/, $1)"}], "group": {"filter": "ITO-Cluster"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "HyperV VMs Critical"}, "options": {"showDisabledItems": false, "skipEmptyValues": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 0}], "timeRegions": [], "title": "HyperV VMs Critical", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 20, "y": 18}, "id": 18, "links": [], "maxDataPoints": 100, "options": {"displayMode": "gradient", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}, "valueMode": "color"}, "pluginVersion": "9.5.15", "targets": [{"application": {"filter": "Status"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:428", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["5", "avg"], "text": "top(5, avg)"}, {"$$hashKey": "object:429", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: Uptime(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: Uptime(.*)/, $1)"}], "group": {"filter": "ITO-Cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "Uptime"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "System uptime", "type": "bargauge"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 24}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "total", "sortDesc": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "/.*ClusterStorage*/"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:177", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:179", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["ClusterStorage", ":"], "text": "replaceAlias(ClusterStorage, :)"}, {"$$hashKey": "object:205", "added": false, "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/hv-sa-03.scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/hv-sa-03.scalepoint.tech(.*)/, $1)"}, {"$$hashKey": "object:178", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/Total space(.*)/", "$1"], "text": "replaceAlias(/Total space(.*)/, $1)"}], "group": {"filter": "ITO-Cluster"}, "hide": false, "host": {"filter": "hv-sa-03.scalepoint.tech"}, "intervalFactor": 1, "item": {"filter": "/.*Total space*/"}, "itemTag": {"filter": "/.*ClusterStorage*/"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "ClusterStorage Total space", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "series", "show": true, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:510", "format": "bytes", "logBase": 1, "show": true}, {"$$hashKey": "object:511", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 35}, "hiddenSeries": false, "id": 21, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "total", "sortDesc": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.3.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "/.*ClusterStorage*/"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:361", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:364", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/Total space(.*)/", "$1"], "text": "replaceAlias(/Total space(.*)/, $1)"}, {"$$hashKey": "object:391", "added": false, "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/CPU utilization(.*)/", "$1"], "text": "replaceAlias(/CPU utilization(.*)/, $1)"}, {"$$hashKey": "object:362", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["ClusterStorage", ":"], "text": "replaceAlias(ClusterStorage, :)"}], "group": {"filter": "ITO-Cluster"}, "hide": false, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "CPU utilization", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "series", "show": true, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:510", "format": "percent", "logBase": 1, "show": true}, {"$$hashKey": "object:511", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "PGHW-CLU01 - Internal cluster", "uid": "HHXI1AnMz", "version": 9, "weekStart": ""}