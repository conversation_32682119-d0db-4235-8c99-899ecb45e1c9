{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 79, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "NGHW -clu2- CSV Used Space %", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 27, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 0, "links": [], "maxDataPoints": "", "maxPerRow": 12, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:223", "alias": ""}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "dsType": "elasticsearch", "evaltype": "0", "functions": [{"$$hashKey": "object:142", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:143", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/ClusterStorage(.*)/", "$1"], "text": "replaceAlias(/ClusterStorage(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "nghw-1wplly3.scalepoint.tech"}, "item": {"filter": "/.*Space utilization */"}, "itemTag": {"filter": ""}, "macro": {"filter": ""}, "metrics": [{"id": "1", "type": "count"}], "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "D", "resultFormat": "time_series", "schema": 12, "slaProperty": "sla", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "target": "", "textFilter": "", "timeField": "timestamp", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}, {"application": {"filter": "Cluster CSV File System"}, "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "dsType": "elasticsearch", "evaltype": "0", "functions": [{"$$hashKey": "object:165", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:166", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/Cluster CSV File System(.*)/", "$1"], "text": "replaceAlias(/Cluster CSV File System(.*)/, $1)"}], "group": {"filter": "Hyper-V"}, "hide": false, "host": {"filter": "nghw-clu2.scalepoint.tech"}, "item": {"filter": "Cluster CSV File System DEPLOY total"}, "itemTag": {"filter": ""}, "macro": {"filter": ""}, "metrics": [{"id": "1", "type": "count"}], "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "slaProperty": "sla", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "target": "", "textFilter": "", "timeField": "timestamp", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [{"$$hashKey": "object:240", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 95, "yaxis": "left"}, {"$$hashKey": "object:241", "colorMode": "warning", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}, {"$$hashKey": "object:242", "colorMode": "custom", "fill": true, "fillColor": "#bf1b00", "line": false, "op": "gt", "value": 98, "yaxis": "left"}], "timeRegions": [], "title": "NGHW -clu2- CSV Used Space %", "tooltip": {"shared": false, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "series", "show": false, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:61", "format": "percent", "label": "Percent of used space", "logBase": 1, "show": true}, {"$$hashKey": "object:62", "format": "percent", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 10, "x": 0, "y": 8}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "HyperV Host"}, "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "evaltype": "0", "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:228", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["10", "avg"], "text": "top(10, avg)"}, {"$$hashKey": "object:229", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: HyperV Logical CPU Total Runtime %(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: HyperV Logical CPU Total Runtime %(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "HyperV Logical CPU Total Runtime %"}, "itemTag": {"filter": "Application: HyperV Host"}, "macro": {"filter": ""}, "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "schema": 12, "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "textFilter": "", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [{"$$hashKey": "object:133", "colorMode": "warning", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}, {"$$hashKey": "object:134", "colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 95, "yaxis": "left"}], "timeRegions": [], "title": "CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:285", "format": "percent", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:286", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 10, "x": 10, "y": 8}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Memory"}, "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "evaltype": "0", "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:359", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: Memory utilization(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: Memory utilization(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "Memory utilization"}, "itemTag": {"filter": "Application: Memory"}, "macro": {"filter": ""}, "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "textFilter": "", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [{"$$hashKey": "object:458", "colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 10000, "yaxis": "left"}], "timeRegions": [], "title": "Memory utilization", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:414", "format": "percent", "logBase": 1, "show": true}, {"$$hashKey": "object:415", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 20, "y": 8}, "id": 29, "links": [], "options": {"displayMode": "gradient", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}, "valueMode": "color"}, "pluginVersion": "9.5.15", "targets": [{"application": {"filter": "Status"}, "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "evaltype": "0", "functions": [{"$$hashKey": "object:614", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["15", "avg"], "text": "top(15, avg)"}, {"$$hashKey": "object:615", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: Uptime(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: Uptime(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "item": {"filter": "Uptime"}, "itemTag": {"filter": ""}, "macro": {"filter": ""}, "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "textFilter": "", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "title": "System uptime", "transformations": [{"id": "merge", "options": {"reducers": []}}], "type": "bargauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "General"}, "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "evaltype": "0", "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:647", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:648", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: Number of processes(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: Number of processes(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "Number of processes"}, "itemTag": {"filter": ""}, "macro": {"filter": ""}, "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "textFilter": "", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [{"$$hashKey": "object:711", "colorMode": "warning", "fill": true, "line": true, "op": "gt", "value": 300, "yaxis": "left"}, {"$$hashKey": "object:712", "colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 400, "yaxis": "left"}], "timeRegions": [], "title": "Number of processes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 12, "y": 16}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Filesystem C:"}, "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "evaltype": "0", "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:752", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: C:: Space utilization(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: C:: Space utilization(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "C:: Space utilization"}, "itemTag": {"filter": ""}, "macro": {"filter": ""}, "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "textFilter": "", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [], "timeRegions": [], "title": "Disk utilization on C:", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 2, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 4, "x": 20, "y": 16}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "HyperV Host"}, "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "evaltype": "0", "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:621", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["9", "avg"], "text": "top(9, avg)"}, {"$$hashKey": "object:622", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: HyperV VMs Critical(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: HyperV VMs Critical(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "HyperV VMs Critical"}, "itemTag": {"filter": ""}, "macro": {"filter": ""}, "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "textFilter": "", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [{"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 0}], "timeRegions": [], "title": "HyperV VMs Critical", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 17, "w": 24, "x": 0, "y": 25}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "/.*Filesystem C*/"}, "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "evaltype": "0", "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:276", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "max"], "text": "top(30, max)"}, {"$$hashKey": "object:302", "added": false, "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}, {"$$hashKey": "object:277", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/ClusterStorage(.*)/", "$1"], "text": "replaceAlias(/ClusterStorage(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "intervalFactor": 1, "item": {"filter": "/.*Total space*/"}, "itemTag": {"filter": "/.*Filesystem C*/"}, "macro": {"filter": ""}, "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "textFilter": "", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [], "timeRegions": [], "title": "PROD Cluster CSV Allocated Space per CSV", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "series", "show": false, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:277", "format": "bytes", "logBase": 1, "show": true}, {"$$hashKey": "object:278", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "Waiting time nghw-1wplly3.scalepoint.tech", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 42}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": "", "maxPerRow": 12, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:223", "alias": ""}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "dsType": "elasticsearch", "evaltype": "0", "functions": [{"$$hashKey": "object:337", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["50", "max"], "text": "top(50, max)"}], "group": {"filter": "nghw-clu2"}, "hide": false, "host": {"filter": "nghw-1wplly3.scalepoint.tech"}, "item": {"filter": "/.*waiting*/"}, "itemTag": {"filter": ""}, "macro": {"filter": ""}, "metrics": [{"id": "1", "type": "count"}], "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "slaProperty": "sla", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "target": "", "textFilter": "", "timeField": "timestamp", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [], "timeRegions": [], "title": "Waiting time nghw-1wplly3.scalepoint.tech", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:61", "format": "s", "label": "", "logBase": 1, "show": true}, {"$$hashKey": "object:62", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "Waiting time nghw-2wplly3.scalepoint.tech", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 54}, "hiddenSeries": false, "id": 31, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": "", "maxPerRow": 12, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:223", "alias": ""}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "dsType": "elasticsearch", "evaltype": "0", "functions": [{"$$hashKey": "object:397", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["50", "max"], "text": "top(50, max)"}], "group": {"filter": "nghw-clu2"}, "hide": false, "host": {"filter": "nghw-2wplly3.scalepoint.tech"}, "item": {"filter": "/.*waiting*/"}, "itemTag": {"filter": ""}, "macro": {"filter": ""}, "metrics": [{"id": "1", "type": "count"}], "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "slaProperty": "sla", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "target": "", "textFilter": "", "timeField": "timestamp", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [], "timeRegions": [], "title": "Waiting time nghw-2wplly3.scalepoint.tech", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:61", "format": "s", "label": "", "logBase": 1, "show": true}, {"$$hashKey": "object:62", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "Waiting time nghw-3wplly3.scalepoint.tech", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 66}, "hiddenSeries": false, "id": 35, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": "", "maxPerRow": 12, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:223", "alias": ""}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "dsType": "elasticsearch", "evaltype": "0", "functions": [{"$$hashKey": "object:521", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["50", "max"], "text": "top(50, max)"}], "group": {"filter": "nghw-clu2"}, "hide": false, "host": {"filter": "nghw-3wplly3.scalepoint.tech"}, "item": {"filter": "/.*waiting*/"}, "itemTag": {"filter": ""}, "macro": {"filter": ""}, "metrics": [{"id": "1", "type": "count"}], "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "slaProperty": "sla", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "target": "", "textFilter": "", "timeField": "timestamp", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [], "timeRegions": [], "title": "Waiting time nghw-3wplly3.scalepoint.tech", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:61", "format": "s", "label": "", "logBase": 1, "show": true}, {"$$hashKey": "object:62", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "Waiting time nghw-4wplly3.scalepoint.tech", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 78}, "hiddenSeries": false, "id": 34, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": "", "maxPerRow": 12, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:223", "alias": ""}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "dsType": "elasticsearch", "evaltype": "0", "functions": [{"$$hashKey": "object:577", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["50", "max"], "text": "top(50, max)"}], "group": {"filter": "nghw-clu2"}, "hide": false, "host": {"filter": "nghw-4wplly3.scalepoint.tech"}, "item": {"filter": "/.*waiting*/"}, "itemTag": {"filter": ""}, "macro": {"filter": ""}, "metrics": [{"id": "1", "type": "count"}], "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "slaProperty": "sla", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "target": "", "textFilter": "", "timeField": "timestamp", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [], "timeRegions": [], "title": "Waiting time nghw-4wplly3.scalepoint.tech", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:61", "format": "s", "label": "", "logBase": 1, "show": true}, {"$$hashKey": "object:62", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "description": "CPU utilization", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 90}, "hiddenSeries": false, "id": 38, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": "", "maxPerRow": 12, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.5.15", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:223", "alias": ""}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "bucketAggs": [{"id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "countTriggersBy": "", "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "dsType": "elasticsearch", "evaltype": "0", "functions": [{"$$hashKey": "object:929", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["50", "max"], "text": "top(50, max)"}, {"$$hashKey": "object:960", "added": false, "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}, {"$$hashKey": "object:958", "added": false, "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/CPU utilization(.*)/", "$1"], "text": "replaceAlias(/CPU utilization(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "hide": false, "host": {"filter": "/.*/"}, "item": {"filter": "CPU utilization"}, "itemTag": {"filter": ""}, "macro": {"filter": ""}, "metrics": [{"id": "1", "type": "count"}], "options": {"count": true, "disableDataAlignment": false, "minSeverity": 3, "showDisabledItems": false, "skipEmptyValues": false, "useTrends": "default", "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "schema": 12, "slaProperty": "sla", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "target": "", "textFilter": "", "timeField": "timestamp", "trigger": {"filter": ""}, "triggers": {"acknowledged": 2}}], "thresholds": [], "timeRegions": [], "title": "CPU utilization", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:61", "format": "percent", "label": "", "logBase": 1, "show": true}, {"$$hashKey": "object:62", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": ["hyper-v,", "clusters", "nghw-cl1", "sentia"], "templating": {"list": []}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "NGHW-CLU2", "version": 1, "weekStart": ""}