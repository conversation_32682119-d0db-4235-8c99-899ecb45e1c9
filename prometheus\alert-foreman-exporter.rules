groups:
- name: foreman-exporter
  rules:
  - alert: Foreman Exporter - hosts from Windows Production Environment have bad statuses.
    expr: sum_over_time(foreman_exporter_hosts{configuration_label="Error",domain!~"spcph.local",puppet_environment="prod_win"}[3h])>500 and (last_over_time(foreman_exporter_hosts{configuration_label="Error",domain!~"spcph.local",puppet_environment="prod_win"}[3h]) !=0)
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "prod"
      domain: "{{ $labels.domain }}"
      hostname: "{{ $labels.hostname }}"
      configuration: "{{ $labels.configuration_label }}"
    annotations:
      summary: 'Foreman Exporter - host {{ $labels.hostname }} from Windows Production Environment has bad statuses. This can be critical - fix errors, as every pass of agents can affect changes'
      job: "{{ $labels.job }}"
  - alert: Foreman Exporter - absent data for hosts
    expr: absent (foreman_exporter_hosts)
    for: 15m
    labels:
      severity: page
      cluster: "{{ $labels.cluster }}"
      app: "{{ $labels.app }}"
      team: "{{ $labels.team }}"
      env: "prod"
      domain: "{{ $labels.domain }}"
      hostname: "{{ $labels.hostname }}"
      configuration: "{{ $labels.configuration_label }}"
    annotations:
      summary: 'Foreman Exporter - absent data for hosts. Check foreman-exporter target or foreman host itself'
      job: "{{ $labels.job }}"
