{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Dashboard to view multiple servers", "editable": false, "gnetId": 405, "graphTooltip": 0, "id": 7, "links": [], "panels": [{"content": "", "editable": true, "error": false, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0}, "id": 11, "minSpan": 4, "mode": "html", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "style": {}, "title": "$node", "type": "text"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "prometheus", "editable": true, "error": false, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 3}, "id": 20, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "minSpan": 4, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "count(node_cpu_seconds_total{instance=~\"$node\", mode=\"system\"}) or count(node_cpu{instance=~\"$node\", mode=\"system\"})", "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 14400, "target": ""}], "thresholds": "", "title": "CPU Cores", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "decimals": 3, "editable": true, "error": false, "fill": 10, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 6}, "id": 7, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 0, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": true, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(irate(node_cpu_seconds_total{mode=\"system\",instance=~'$node'}[5m])) or sum(irate(node_cpu{mode=\"system\",instance=~'$node'}[5m]))", "interval": "", "intervalFactor": 2, "legendFormat": "{{mode}}", "metric": "", "refId": "A", "step": 1200, "target": ""}, {"expr": "sum(irate(node_cpu_seconds_total{mode=\"user\",instance=~'$node'}[5m])) or sum(irate(node_cpu{mode=\"user\",instance=~'$node'}[5m]))", "interval": "", "intervalFactor": 2, "legendFormat": "user", "refId": "B", "step": 1200}, {"expr": "sum(irate(node_cpu_seconds_total{mode=\"nice\",instance=~'$node'}[5m])) or sum(irate(node_cpu{mode=\"nice\",instance=~'$node'}[5m]))", "interval": "", "intervalFactor": 2, "legendFormat": "nice", "refId": "C", "step": 1200}, {"expr": "sum(irate(node_cpu_seconds_total{mode=\"iowait\",instance=~'$node'}[5m])) or sum(irate(node_cpu{mode=\"iowait\",instance=~'$node'}[5m]))", "interval": "", "intervalFactor": 2, "legendFormat": "iowait", "refId": "E", "step": 1200}, {"expr": "sum(irate(node_cpu_seconds_total{mode=\"steal\",instance=~'$node'}[5m])) or sum(irate(node_cpu{mode=\"steal\",instance=~'$node'}[5m]))", "intervalFactor": 2, "legendFormat": "steal", "refId": "H", "step": 1200}, {"expr": "sum(irate(node_cpu_seconds_total{mode=\"idle\",instance=~'$node'}[5m])) or sum(irate(node_cpu{mode=\"idle\",instance=~'$node'}[5m]))", "interval": "", "intervalFactor": 2, "legendFormat": "idle", "refId": "D", "step": 1200}, {"expr": "sum(irate(node_cpu_seconds_total{mode=\"irq\",instance=~'$node'}[5m])) or sum(irate(node_cpu{mode=\"irq\",instance=~'$node'}[5m]))", "interval": "", "intervalFactor": 2, "legendFormat": "irq", "refId": "F", "step": 1200}, {"expr": "sum(irate(node_cpu_seconds_total{mode=\"softirq\",instance=~'$node'}[5m])) or sum(irate(node_cpu{mode=\"softirq\",instance=~'$node'}[5m]))", "interval": "", "intervalFactor": 2, "legendFormat": "softirq", "refId": "G", "step": 1200}, {"expr": "sum(irate(node_cpu_seconds_total{mode=\"guest\",instance=~'$node'}[5m])) or sum(irate(node_cpu{mode=\"guest\",instance=~'$node'}[5m]))", "interval": "", "intervalFactor": 2, "legendFormat": "guest", "refId": "I", "step": 1200}], "thresholds": [{"colorMode": "custom", "fill": true, "fillColor": "rgba(216, 200, 27, 0.27)", "op": "gt", "value": 0}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "%", "logBase": 1, "max": 100, "min": 0, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Slab": "#E5A8E2", "Swap": "#E24D42"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "decimals": 2, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 13}, "id": 17, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [{"alias": "/Apps|Buffers|Cached|Free|Slab|SwapCached|PageTables|VmallocUsed/", "fill": 5, "stack": true}, {"alias": "<PERSON><PERSON><PERSON>", "fill": 5, "stack": true}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "( node_memory_MemTotal_bytes{instance=~'$node'} - node_memory_MemFree_bytes{instance=~'$node'} - node_memory_Buffers_bytes{instance=~'$node'} - node_memory_Cached_bytes{instance=~'$node'} - node_memory_SwapCached_bytes{instance=~'$node'} - node_memory_Slab_bytes{instance=~'$node'} - node_memory_PageTables_bytes{instance=~'$node'} - node_memory_VmallocUsed_bytes{instance=~'$node'} ) or ( node_memory_MemTotal{instance=~'$node'} - node_memory_MemFree{instance=~'$node'} - node_memory_Buffers{instance=~'$node'} - node_memory_Cached{instance=~'$node'} - node_memory_SwapCached{instance=~'$node'} - node_memory_Slab{instance=~'$node'} - node_memory_PageTables{instance=~'$node'} - node_memory_VmallocUsed{instance=~'$node'} )", "interval": "", "intervalFactor": 2, "legendFormat": "Apps", "metric": "", "refId": "A", "step": 1200, "target": ""}, {"expr": "node_memory_Buffers_bytes{instance=~'$node'} or node_memory_Buffers{instance=~'$node'}", "interval": "", "intervalFactor": 2, "legendFormat": "Buffers", "refId": "B", "step": 1200}, {"expr": "node_memory_Cached_bytes{instance=~'$node'} or node_memory_Cached{instance=~'$node'}", "interval": "", "intervalFactor": 2, "legendFormat": "<PERSON><PERSON><PERSON>", "refId": "D", "step": 1200}, {"expr": "node_memory_MemFree_bytes{instance=~'$node'} or node_memory_MemFree{instance=~'$node'}", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "Free", "refId": "E", "step": 1200}, {"expr": "node_memory_Slab_bytes{instance=~'$node'} or node_memory_Slab{instance=~'$node'}", "interval": "", "intervalFactor": 2, "legendFormat": "Slab", "refId": "F", "step": 1200}, {"expr": "node_memory_SwapCached_bytes{instance=~'$node'} or node_memory_SwapCached{instance=~'$node'}", "interval": "", "intervalFactor": 2, "legendFormat": "SwapCached", "refId": "G", "step": 1200}, {"expr": "node_memory_PageTables_bytes{instance=~'$node'} or node_memory_PageTables{instance=~'$node'}", "interval": "", "intervalFactor": 2, "legendFormat": "PageTables", "refId": "H", "step": 1200}, {"expr": "node_memory_VmallocUsed_bytes{instance=~'$node'} or node_memory_VmallocUsed{instance=~'$node'}", "interval": "", "intervalFactor": 2, "legendFormat": "VmallocUsed", "metric": "", "refId": "I", "step": 1200}, {"expr": "(node_memory_SwapTotal_bytes{instance=~'$node'} - node_memory_SwapFree{instance=~'$node'}) or (node_memory_SwapTotal{instance=~'$node'} - node_memory_SwapFree{instance=~'$node'})", "interval": "", "intervalFactor": 2, "legendFormat": "<PERSON><PERSON><PERSON>", "metric": "", "refId": "C", "step": 1200}, {"expr": "node_memory_Committed_AS_bytes{instance=~'$node'} or node_memory_Committed_AS{instance=~'$node'}", "interval": "", "intervalFactor": 2, "legendFormat": "Committed", "metric": "", "refId": "J", "step": 1200}, {"expr": "node_memory_Mapped_bytes{instance=~'$node'} or node_memory_Mapped{instance=~'$node'}", "interval": "", "intervalFactor": 2, "legendFormat": "Mapped", "refId": "K", "step": 1200}, {"expr": "node_memory_Active_bytes{instance=~'$node'} or node_memory_Active{instance=~'$node'}", "interval": "", "intervalFactor": 2, "legendFormat": "Active", "metric": "", "refId": "L", "step": 1200}, {"expr": "node_memory_Inactive_bytes{instance=~'$node'} or node_memory_Inactive{instance=~'$node'}", "interval": "", "intervalFactor": 2, "legendFormat": "Inactive", "metric": "", "refId": "M", "step": 1200}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "GB", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 20}, "id": 13, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_load1{instance=~\"$node\"}", "interval": "", "intervalFactor": 2, "legendFormat": "load", "metric": "", "refId": "A", "step": 1200, "target": ""}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Load", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "decimals": 3, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 27}, "id": 9, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "100.0 - 100 * (node_filesystem_avail_bytes{instance=~'$node',device !~'tmpfs',device!~'by-uuid'} / node_filesystem_size_bytes{instance=~'$node',device !~'tmpfs',device!~'by-uuid'}) or 100.0 - 100 * (node_filesystem_avail{instance=~'$node',device !~'tmpfs',device!~'by-uuid'} / node_filesystem_size{instance=~'$node',device !~'tmpfs',device!~'by-uuid'})", "interval": "", "intervalFactor": 2, "legendFormat": "{{mountpoint}}", "metric": "", "refId": "A", "step": 1200, "target": ""}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Space Used", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "max": 100, "min": 0, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 34}, "id": 19, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_disk_io_time_seconds_total{instance=~\"$node\"}[5m])/10 or irate(node_disk_io_time_ms{instance=~\"$node\"}[5m])/10", "interval": "", "intervalFactor": 2, "legendFormat": "{{device}}", "metric": "", "refId": "A", "step": 1200, "target": ""}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Utilization per Device", "tooltip": {"msResolution": false, "shared": false, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "max": 100, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 41}, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [{"alias": "/.*_read$/", "transform": "negative-Y"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_disk_reads_completed_total{instance=~'$node'}[5m]) or irate(node_disk_reads_completed{instance=~'$node'}[5m])", "interval": "", "intervalFactor": 4, "legendFormat": "{{device}}_read", "metric": "", "refId": "A", "step": 2400, "target": ""}, {"expr": "irate(node_disk_writes_completed_total{instance=~'$node'}[5m]) or irate(node_disk_writes_completed{instance=~'$node'}[5m])", "intervalFactor": 2, "legendFormat": "{{device}}_write", "metric": "", "refId": "B", "step": 1200}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk IOs per Device", "tooltip": {"msResolution": false, "shared": false, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "IO/second read (-) / write (+)", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 48}, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [{"alias": "/.*_read/", "transform": "negative-Y"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_disk_read_bytes_total{instance=~'$node'}[5m]) * 512 or irate(node_disk_sectors_read{instance=~'$node'}[5m]) * 512", "interval": "", "intervalFactor": 4, "legendFormat": "{{device}}_read", "refId": "B", "step": 2400}, {"expr": "irate(node_disk_written_bytes_total{instance=~'$node'}[5m]) * 512 or irate(node_disk_sectors_written{instance=~'$node'}[5m]) * 512", "interval": "", "intervalFactor": 4, "legendFormat": "{{device}}_write", "metric": "", "refId": "A", "step": 2400, "target": ""}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Throughput per Device", "tooltip": {"msResolution": false, "shared": false, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes/second read (-) / write (+)", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 55}, "id": 22, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_context_switches_total{instance=~\"$node\"}[5m]) or irate(node_context_switches{instance=~\"$node\"}[5m])", "interval": "", "intervalFactor": 2, "legendFormat": "context switches", "metric": "", "refId": "A", "step": 1200, "target": ""}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Context Switches", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 62}, "id": 12, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [{"alias": "/.*_in/", "transform": "negative-Y"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_receive_bytes_total{instance=~'$node'}[5m])*8 or irate(node_network_receive_bytes{instance=~'$node'}[5m])*8", "interval": "", "intervalFactor": 2, "legendFormat": "{{device}}_in", "metric": "", "refId": "A", "step": 1200, "target": ""}, {"expr": "irate(node_network_transmit_bytes_total{instance=~'$node'}[5m])*8 or irate(node_network_transmit_bytes{instance=~'$node'}[5m])*8", "interval": "", "intervalFactor": 2, "legendFormat": "{{device}}_out", "refId": "B", "step": 1200}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Network Traffic", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bits", "label": "bits in (-) / bits out (+)", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 69}, "id": 21, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_netstat_Tcp_CurrEstab{instance=~'$node'}", "intervalFactor": 2, "legendFormat": "established", "refId": "A", "step": 1200, "target": ""}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Netstat", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 76}, "id": 23, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [{"alias": "/.*Out.*/", "transform": "negative-Y"}, {"alias": "Udp_NoPorts", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_netstat_Udp_InDatagrams{instance=~\"$node\"}[5m])", "intervalFactor": 2, "legendFormat": "Udp_InDatagrams", "refId": "A", "step": 1200, "target": ""}, {"expr": "irate(node_netstat_Udp_InErrors{instance=~\"$node\"}[5m])", "intervalFactor": 2, "legendFormat": "Udp_InErrors", "refId": "B", "step": 1200}, {"expr": "irate(node_netstat_Udp_OutDatagrams{instance=~\"$node\"}[5m])", "interval": "", "intervalFactor": 2, "legendFormat": "Udp_OutDatagrams", "refId": "C", "step": 1200}, {"expr": "irate(node_netstat_Udp_NoPorts{instance=~\"$node\"}[5m])", "intervalFactor": 2, "legendFormat": "Udp_NoPorts", "refId": "D", "step": 1200}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "UDP Stats", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 83}, "id": 24, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "minSpan": 4, "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": "node", "scopedVars": {"node": {"selected": true, "text": "node-exporter:9100", "value": "node-exporter:9100"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_nf_conntrack_entries_limit{instance=~\"$node\"} - node_nf_conntrack_entries{instance=~\"$node\"}", "intervalFactor": 2, "legendFormat": "free", "refId": "A", "step": 1200, "target": ""}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Conntrack", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 16, "style": "dark", "tags": ["node-exporter", "prometheus", "linux"], "templating": {"list": [{"allFormat": "glob", "allValue": null, "current": {"selected": false, "text": "node-exporter:9100", "value": "node-exporter:9100"}, "datasource": "prometheus", "definition": "", "hide": 0, "includeAll": false, "label": "", "multi": true, "multiFormat": "regex values", "name": "node", "options": [], "query": "label_values(node_exporter_build_info, instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {"now": true, "refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Node Exporter Server Metrics", "uid": "JeCj_nhik", "version": 1}