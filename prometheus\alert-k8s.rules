groups:
# Blackbox
- name: Blackbox Kubernetes related alerts
  rules:
  # ENV=PROD, APP=KUBERNETES
  - alert: Webpages - https/http from PROD env and KUBERNETES app probe failed
    expr: probe_success{job="blackbox_prod_kubernetes",app="kubernetes"} != 1
    for: 3m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
    annotations:
      summary: 'Webpages - https/http from PROD env and Kubernetes app probe failed {{ $labels.instance }} last value: {{ $value | printf "%.2f" }}'
  # ENV=PROD, APP=KUBERNETES
  - alert: Webpages - https/http from PROD env and KUBERNETES app response rate is rising
    expr: sum by (instance) (rate (probe_http_duration_seconds { job="blackbox_prod_kubernetes"}[3m]))>1
    for: 3m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "{{ $labels.env }}"
      app: "{{ $labels.app }}"
    annotations:
      summary: 'Webpages - https/http from PROD env and KUBERNETES app response rate is rising {{ $labels.instance }} last value: {{ $value | printf "%.2f" }}'
# K8S -prod-rn01 alerts
- name: K8S Production applications/webpages related alerts
  rules:
  # # subrogation-service-prod absent data
  # - alert: K8S Prod - Subrogation Service pods count too low - should be 3
  #   expr: count (kube_pod_container_status_ready {namespace="subrogation-service-prod", prometheus_from="prod-rn01"}) by (instance) < 3
  #   for: 3m
  #   labels:
  #     severity: page
  #     job: "{{ $labels.job }}"
  #     env: "prod"
  #     app: "subrogation-service"
  #     k8s: "{{ $labels.prometheus_from }}"
  #     service: "{{ $labels.service }}"
  #   annotations:
  #     summary: "K8s Prod - Subrogation Service pod count is too low for 3m - https://prod-rn01.scalepoint.tech/p/local:p-ljhvg/workload/deployment:subrogation-service-prod:subrogation-service-prod-sp-generic-web"
  # # subrogation-service-prod absent data
  # - alert: K8S Prod - Subrogation Service absent data for last 5 minutes
  #   expr: absent (kube_pod_container_status_ready{namespace="subrogation-service-prod",prometheus_from="prod-rn01"})
  #   for: 5m
  #   labels:
  #     severity: page
  #     job: "{{ $labels.job }}"
  #     env: "prod"
  #     app: "subrogation-service"
  #     k8s: "{{ $labels.prometheus_from }}"
  #     service: "{{ $labels.service }}"
  #   annotations:
  #     summary: "K8s Prod - Subrogation Service absent data for pods for last 5 minutes - https://prod-rn01.scalepoint.tech/p/local:p-ljhvg/workload/deployment:subrogation-service-prod:subrogation-service-prod-sp-generic-web"
  # trws-prod
  # - alert: K8S Prod - TRWS pods count too low - should be 3
  #   expr: count (kube_pod_container_status_ready {namespace="trws-prod", prometheus_from="prod-rn01"}) by (instance) < 3
  #   for: 3m
  #   labels:
  #     severity: page
  #     job: "{{ $labels.job }}"
  #     env: "prod"
  #     app: "trws"
  #     k8s: "{{ $labels.prometheus_from }}"
  #     service: "{{ $labels.service }}"
  #   annotations:
  #     summary: "K8s Prod - TRWS pod count is too low for 3m - https://prod-rn01.scalepoint.tech/p/local:p-ljhvg/workload/deployment:trws-prod:trws-prod-sp-generic-web"
  # # trws-prod absent data
  # - alert: K8S Prod - TRWS absent data for last 5 minutes
  #   expr: absent (kube_pod_container_status_ready{namespace="trws-prod",prometheus_from="prod-rn01"})
  #   for: 5m
  #   labels:
  #     severity: page
  #     job: "{{ $labels.job }}"
  #     env: "prod"
  #     app: "trws"
  #     k8s: "{{ $labels.prometheus_from }}"
  #     service: "{{ $labels.service }}"
  #   annotations:
  #     summary: "K8s Prod - TRWS absent data for pods for last 5 minutes - https://prod-rn01.scalepoint.tech/p/local:p-ljhvg/workload/deployment:bi-survey-service-prod:bi-survey-service-prod-sp-generic-web"
# K8S - prod-namespaced related alerts
- name: K8S Production application states related alerts
  rules:
  - alert: K8S Deployment state is not in sync with its desired state for 10 minutes
    expr: (kube_deployment_spec_replicas{job="kube-state-metrics",namespace=~".*prod.*"} != kube_deployment_status_replicas_available{job="kube-state-metrics",namespace=~".*prod.*"})
    for: 10m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "prod"
      app: "prometheus"
      k8s: "{{ $labels.prometheus_from }}"
      service: "{{ $labels.service }}"
      namespace: "{{ $labels.namespace }}"
    annotations:
      summary: "K8S Deployment state is not in sync with its desired state"
  - alert: K8S Deployment state (over 15m) is not in sync with its desired state
    expr: (kube_deployment_spec_replicas{job="kube-state-metrics",namespace=~".*prod.*"} != kube_deployment_status_replicas_available{job="kube-state-metrics",namespace=~".*prod.*"}) and (changes(kube_deployment_status_replicas_updated{job="kube-state-metrics",namespace=~".*prod.*"}[15m]) )
    for: 10m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "prod"
      app: "prometheus"
      k8s: "{{ $labels.prometheus_from }}"
      service: "{{ $labels.service }}"
      namespace: "{{ $labels.namespace }}"
    annotations:
      summary: "K8S Deployment state (over 15m) is not in sync with its desired state"


### Alerts for production cluster
- name: scalepoint-prod node health related alerts
  rules:
  - alert: scalepoint-prod - Prometheus not working
    expr: up{instance="prometheus.scalepoint-prod.scalepoint.tech:443",job="federate_scalepoint-prod"} <1
    for: 5m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "prod"
      app: "prometheus"
      k8s: "{{ $labels.prometheus_from }}"
      service: "{{ $labels.service }}"
      namespace: "{{ $labels.namespace }}"
    annotations:
      summary: "scalepoint-prod - Prometheus not working"
  - alert: scalepoint-prod - Prometheus absent data for 5m
    expr: absent (up{instance="prometheus.scalepoint-prod.scalepoint.tech:443",job="federate_scalepoint-prod"})
    for: 5m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "prod"
      app: "prometheus"
      k8s: "{{ $labels.prometheus_from }}"
      service: "{{ $labels.service }}"
      namespace: "{{ $labels.namespace }}"
    annotations:
      summary: "scalepoint-prod - Prometheus absent data for 5m"
  - alert: scalepoint-prod - Number of healthy nodes is too low
    expr: sum (kube_node_status_condition{condition="Ready",service="expose-kubernetes-metrics",status="true",prometheus_from=~"^(prometheus-prod).*"}) by (instance) < 7
    for: 3m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "prod"
      app: "kubernetes"
      k8s: "{{ $labels.prometheus_from }}"
      service: "{{ $labels.service }}"
      namespace: "{{ $labels.namespace }}"
    annotations:
      summary: "scalepoint-prod - Number of healthy nodes is too low"
  - alert: scalepoint-prod - Number of waiting state containers is non 0
    expr: sum by (prometheus_from) (kube_pod_container_status_waiting {prometheus_from=~"^(prometheus-prod).*"}) > 0
    for: 5m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "prod"
      app: "kubernetes"
      k8s: "{{ $labels.prometheus_from }}"
      service: "{{ $labels.service }}"
      namespace: "{{ $labels.namespace }}"
    annotations:
      summary: "scalepoint-prod - Number of waiting state containers is non 0"
  - alert: scalepoint-prod - Node is down/unready for 5m
    expr: (kube_node_status_condition{condition="Ready",prometheus_from=~"^(prometheus-prod).*",service="expose-kubernetes-metrics",status="true"}) <1
    for: 5m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "prod"
      app: "prometheus"
      k8s: "{{ $labels.prometheus_from }}"
      service: "{{ $labels.service }}"
      namespace: "{{ $labels.namespace }}"
    annotations:
      summary: scalepoint-prod - Node is down/unready for 5m"
  - alert: scalepoint-prod - Number of replicas in deployment is below 1
    expr: kube_deployment_status_replicas {prometheus_from=~"^(prometheus-prod).*"} < 1
    for: 10m
    labels:
      severity: page
      job: "{{ $labels.job }}"
      env: "prod"
      app: "kubernetes"
      k8s: "{{ $labels.prometheus_from }}"
      service: "{{ $labels.service }}"
      namespace: "{{ $labels.namespace }}"
    annotations:
      summary: "scalepoint-prod- Number of replicas in deployment is below 1 for 10 minutes - check state of deployment"