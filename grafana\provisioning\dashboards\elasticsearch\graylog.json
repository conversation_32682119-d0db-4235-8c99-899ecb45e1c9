{"annotations": {"list": [{"$$hashKey": "object:188", "builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 1, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": "", "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/(.*).scalepoint.lan.*/", "$1"], "text": "replaceAlias(/(.*).scalepoint.lan.*/, $1)", "$$hashKey": "object:577"}], "group": {"filter": "Graylog Servers"}, "host": {"filter": "/prod-gray.*/"}, "item": {"filter": "Load average (5m avg)"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PROD-GRAYLOG CPU LOAD", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "functions": [], "group": {"filter": "Graylog Servers"}, "host": {"filter": "prod-graylog.scalepoint.lan"}, "item": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "D", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Graylog messages queue", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "zabbix", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 8}, "id": 8, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.3", "targets": [{"application": {"filter": "Interface eth0"}, "functions": [{"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": [".scalepoint.lan: Interface eth0: Bits sent", ":"], "text": "replaceAlias(.scalepoint.lan: Interface eth0: Bits sent, :)", "added": false, "$$hashKey": "object:1202"}], "group": {"filter": "Graylog Servers"}, "host": {"filter": "/.*prod-graylog[1-5]*/"}, "item": {"filter": "Interface eth0: Bits sent"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "timeFrom": null, "timeShift": null, "title": " Interface eth0: Bits sent", "type": "stat"}, {"cacheTimeout": null, "datasource": "zabbix", "fieldConfig": {"defaults": {"decimals": 2, "displayName": "", "mappings": [{"$$hashKey": "object:338", "id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 12, "y": 8}, "id": 5, "links": [], "maxDataPoints": "", "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": true, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.5.3", "targets": [{"application": {"filter": "Filesystem /data"}, "functions": [{"def": {"category": "Aggregate", "defaultParams": ["1m"], "name": "average", "params": [{"name": "interval", "type": "string"}]}, "params": ["1m"], "text": "average(1m)", "$$hashKey": "object:735"}], "group": {"filter": "Graylog Servers"}, "host": {"filter": "/.*prod-graylog[1-5]/"}, "item": {"filter": "/data: Space utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Space utilization /data: ", "type": "gauge"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 8}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Filesystem /data"}, "functions": [{"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": [".scalepoint.lan: /data: Space utilization", ":"], "text": "replaceAlias(.scalepoint.lan: /data: Space utilization, :)", "$$hashKey": "object:1259"}], "group": {"filter": "Graylog Servers"}, "host": {"filter": "/.*prod-graylog[1-5]*/"}, "item": {"filter": "/data: Space utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": " Used space  /data per node", "tooltip": {"shared": false, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "series", "name": null, "show": false, "values": ["current"]}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "zabbix", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 15}, "id": 10, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.3", "targets": [{"application": {"filter": "Interface eth0"}, "functions": [{"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": [".scalepoint.lan: Interface eth0: Bits received", ":"], "text": "replaceAlias(.scalepoint.lan: Interface eth0: Bits received, :)", "$$hashKey": "object:1228"}], "group": {"filter": "Graylog Servers"}, "host": {"filter": "/.*prod-graylog[1-5]*/"}, "item": {"filter": "Interface eth0: Bits received"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "timeFrom": null, "timeShift": null, "title": "Interface eth0: Bits received", "type": "stat"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 15}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Filesystem /"}, "functions": [{"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": [".scalepoint.lan: /: Space utilization", ":"], "text": "replaceAlias(.scalepoint.lan: /: Space utilization, :)", "added": false, "$$hashKey": "object:1048"}], "group": {"filter": "Graylog Servers"}, "host": {"filter": "/.*prod-graylog[1-5]*/"}, "item": {"filter": "/: Space utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 5}, {"colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 1}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Space utilization /:  ", "tooltip": {"shared": false, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "series", "name": null, "show": false, "values": ["current"]}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"ackEventColor": "rgba(0, 0, 0, 0)", "ackField": true, "ageField": false, "customLastChangeFormat": false, "datasource": "zabbix", "descriptionAtNewLine": false, "descriptionField": true, "fieldConfig": {"defaults": {}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 7, "w": 24, "x": 0, "y": 22}, "highlightBackground": false, "highlightNewEvents": true, "highlightNewerThan": "1h", "hostField": true, "hostGroups": false, "hostProxy": false, "hostTechNameField": false, "id": 4, "lastChangeFormat": "", "layout": "table", "limit": 10, "links": [], "markAckEvents": false, "okEventColor": "rgba(0, 245, 153, 0.45)", "pageSize": 25, "problemTimeline": true, "resizedColumns": [], "schemaVersion": 7, "severityField": false, "showTags": true, "sortProblems": "lastchange", "statusField": false, "statusIcon": false, "targets": [{"application": {"filter": ""}, "group": {"filter": "Graylog Servers"}, "host": {"filter": ""}, "options": {"acknowledged": 2, "hostsInMaintenance": true, "limit": 10, "minSeverity": 0, "sortProblems": "default"}, "proxy": {"filter": ""}, "queryType": 5, "refId": "A", "showProblems": "problems", "tags": {"filter": ""}, "trigger": {"filter": ""}}], "title": "Panel Title", "triggerSeverity": [{"color": "#B7DBAB", "priority": 0, "severity": "Not classified", "show": true}, {"color": "#82B5D8", "priority": 1, "severity": "Information", "show": true}, {"color": "#E5AC0E", "priority": 2, "severity": "Warning", "show": true}, {"color": "#C15C17", "priority": 3, "severity": "Average", "show": true}, {"color": "#BF1B00", "priority": 4, "severity": "High", "show": true}, {"color": "#890F02", "priority": 5, "severity": "Disaster", "show": true}], "type": "alexanderzobnin-zabbix-triggers-panel"}], "refresh": "5s", "schemaVersion": 27, "style": "dark", "tags": ["graylog", "zabbix"], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Production Graylog"}