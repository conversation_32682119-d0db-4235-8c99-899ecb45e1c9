{"services": [{"id": "syn-pg.scalepoint.tech", "name": "syn-pg.scalepoint.tech", "address": "syn-pg.scalepoint.tech", "checks": [{"id": "syn-pg.scalepoint.tech", "tcp": "syn-pg.scalepoint.tech:9100", "interval": "60s"}], "tags": ["env=prod", "team=ito", "source=synology", "app=node-exporter", "metrics"], "port": 9100}, {"id": "syn-taa.scalepoint.tech", "name": "syn-taa.scalepoint.tech", "address": "syn-taa.scalepoint.tech", "checks": [{"id": "syn-taa.scalepoint.tech", "tcp": "syn-taa.scalepoint.tech:9100", "interval": "60s"}], "tags": ["env=prod", "team=ito", "source=synology", "app=node-exporter", "metrics"], "port": 9100}]}