{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 11, "x": 0, "y": 0}, "hiddenSeries": false, "id": 1, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "ICMP"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Network devices"}, "host": {"filter": "fg_LUB"}, "intervalFactor": 1, "item": {"filter": "ICMP loss"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "Test TCP"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Network devices"}, "host": {"filter": "fg_LUB"}, "intervalFactor": 1, "item": {"filter": "VPN on 443"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "fg_LUB", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 13, "x": 11, "y": 0}, "hiddenSeries": false, "id": 5, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "ICMP"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Network devices"}, "host": {"filter": "fg_GDA"}, "intervalFactor": 1, "item": {"filter": "ICMP loss"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "Test TCP"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Network devices"}, "host": {"filter": "fg_GDA"}, "intervalFactor": 1, "item": {"filter": "VPN on 443"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "fg_GDA", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 11, "x": 0, "y": 8}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "ICMP"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "host": {"filter": "prod-wks01.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "ICMP loss"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "ICMP"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": true, "host": {"filter": "prod-wks01.scalepoint.lan"}, "intervalFactor": 1, "item": {"filter": "ICMP response time"}, "mode": 0, "options": {"showDisabledItems": false, "skipEmptyValues": false}, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "prod-wks01.scalepoint.lan", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 13, "x": 11, "y": 8}, "hiddenSeries": false, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "ICMP"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "host": {"filter": "corp-dc02.spcph.local"}, "intervalFactor": 1, "item": {"filter": "ICMP loss"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "ICMP"}, "expr": "", "format": "time_series", "functions": [], "group": {"filter": "Windows Servers"}, "hide": true, "host": {"filter": "corp-dc02.spcph.local"}, "intervalFactor": 1, "item": {"filter": "ICMP response time"}, "mode": 0, "options": {"showDisabledItems": false, "skipEmptyValues": false}, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "corp-dc02.spcph.local", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 15}, "hiddenSeries": false, "id": 7, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Test TCP"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["fg_LUB"], "text": "setAlias(fg_LUB)"}], "group": {"filter": "Network devices"}, "host": {"filter": "fg_LUB"}, "intervalFactor": 1, "item": {"filter": "VPN on 443"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "C", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "Test TCP"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["fg_GDA"], "text": "setAlias(fg_GDA)"}], "group": {"filter": "Network devices"}, "host": {"filter": "fg_GDA"}, "intervalFactor": 1, "item": {"filter": "VPN on 443"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "Test TCP"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["fg_UA"], "text": "setAlias(fg_UA)"}], "group": {"filter": "Network devices"}, "host": {"filter": "fg_UA"}, "intervalFactor": 1, "item": {"filter": "VPN on 443"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": "Test TCP"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["VPN_NG_Taastrup"], "text": "setAlias(VPN_NG_Taastrup)"}], "group": {"filter": "Network devices"}, "host": {"filter": "VPN_NG_Taastrup"}, "intervalFactor": 1, "item": {"filter": "VPN on 443"}, "mode": 0, "options": {"showDisabledItems": false}, "refId": "D", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Test connection from ********* to VPN's", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": false, "schemaVersion": 36, "style": "dark", "tags": ["fortigates", "devices", "vpn", "zabbix"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "VPN"}