# config file version
apiVersion: 1
deleteDatasources:
  - name: prometheus
    orgId: 1
  - name: prometheus-test
    orgId: 1
datasources:
- name: prometheus
  type: prometheus
  typeLogoUrl: public/app/plugins/datasource/prometheus/img/prometheus_logo.svg
  access: proxy
  orgId: 1
  url: https://int-prometheus.scalepoint.tech
  basicAuth: false
  password: ""
  user: ""
  database: ""
  isDefault: true
  jsonData:
    httpMethod: POST
  version: 1
  editable: true
  readOnly: false