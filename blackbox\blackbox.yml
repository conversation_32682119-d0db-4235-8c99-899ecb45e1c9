modules:
  http_2xx:
    prober: http
    timeout: 10s
    http:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      valid_status_codes: []
      method: GET
      tls_config:
        insecure_skip_verify: true
  http_2xx_tls:
    prober: http
    timeout: 10s
    http:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      valid_status_codes: []
      method: GET
      tls_config:
        insecure_skip_verify: false
  webdav:
    prober: http
    timeout: 10s
    http:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      valid_status_codes: []
      method: GET
      tls_config:
        insecure_skip_verify: false
      basic_auth:
        username: ito
        password: estate7killer
  http_vpn:
    prober: http
    timeout: 10s
    http:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      valid_status_codes: []
      method: GET
      tls_config:
        insecure_skip_verify: false
  http_overall_ok:
    prober: http
    timeout: 10s
    http:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      valid_status_codes: []
      method: GET
      tls_config:
        insecure_skip_verify: true
      fail_if_body_not_matches_regexp:
      - "Overall results: OK"
  docker_production:
    prober: http
    timeout: 10s
    http:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      valid_status_codes: []
      method: GET
      tls_config:
        insecure_skip_verify: false
  dns_spcph_test:
    prober: dns
    timeout: 15s
    dns:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      query_name: "alive.spcph.test"
      query_type: A
      validate_answer_rrs:
        fail_if_not_matches_regexp:
          - "*******"
  dns_smtp_spcph_local:
    prober: dns
    timeout: 15s
    dns:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      query_name: "int-ito-wsus01.spcph.local"
      query_type: A
  dns_scalepoint_tech:
    prober: dns
    timeout: 15s
    dns:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      query_name: "alive.scalepoint.tech"
      query_type: A
      validate_answer_rrs:
        fail_if_not_matches_regexp:
          - "*******"
  dns_scalepoint_io:
    prober: dns
    timeout: 15s
    dns:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      query_name: "alive.scalepoint.io"
      query_type: A
      validate_answer_rrs:
        fail_if_not_matches_regexp:
          - "*******"
  dns_scalepoint_eu:
    prober: dns
    timeout: 15s
    dns:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      query_name: "alive.scalepoint.eu"
      query_type: A
      validate_answer_rrs:
        fail_if_not_matches_regexp:
          - "*******"
  dns_scalepoint_dk:
    prober: dns
    timeout: 15s
    dns:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      query_name: "alive.scalepoint.dk"
      query_type: A
      validate_answer_rrs:
        fail_if_not_matches_regexp:
          - "*******"
  http_auth_check:
    prober: http
    timeout: 10s
    http:
      ip_protocol_fallback: true
      preferred_ip_protocol: ipv4
      valid_status_codes: [200, 401, 403]  # Akceptuj kody autoryzacji
      method: GET
      tls_config:
        insecure_skip_verify: true
  ssh_banner:
     prober: tcp
     timeout: 15s
     tcp:
       ip_protocol_fallback: true
       preferred_ip_protocol: ipv4
       query_response:
       - expect: "^SSH-2.0-"
         starttls: false
       tls: false
       tls_config:
         insecure_skip_verify: true
