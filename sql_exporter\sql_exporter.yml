# Global settings and defaults.
global:
  scrape_timeout: 10s
  scrape_timeout_offset: 500ms
  min_interval: 0s
  max_connections: 3
  max_idle_connections: 3

jobs:
  # All metrics from all targets get a job label, set to this value.
  - job_name: mssql

    # The set of collectors (defined below) to be applied to all targets in this job.
    collectors: [mssql_standard, mssql_custom]

    # Similar to the Prometheus configuration, multiple sets of targets may be defined, each with an optional set of
    # labels to be applied to all metrics.
    static_configs:
      - targets:
          'mssql_database_eca': 'sqlserver://zabbix_reader:<EMAIL>/eca?database=master'
          'mssql_database_shop': 'sqlserver://zabbix_reader:<EMAIL>/shop?database=master'
          'mssql_database_ecb': 'sqlserver://zabbix_reader:Yippee3-Cha<PERSON>-<EMAIL>/ecb?database=master'
          'mssql_database_ecc': 'sqlserver://zabbix_reader:Yippee3-Cha<PERSON>-<EMAIL>/ecc?database=master'
          'mssql_database_SHARED': 'sqlserver://zabbix_reader:<EMAIL>/SHARED?database=master'
          'mssql_database_INTERNAL': 'sqlserver://zabbix_reader:<EMAIL>/INTERNAL?database=master'
        labels:
          environment: production
# The targets to monitor and the collectors to execute on them.
#target:
#  name: mssql_database_shop
#  data_source_name: 'sqlserver://zabbix_reader:<EMAIL>/shop?database=master'
#  collectors: [mssql_standard, mssql_custom]
#  enable_ping: true
#
#  - name: mssql_database_eca
#    data_source_name: 'sqlserver://zabbix_reader:<EMAIL>/eca?database=master'
#    collectors: [mssql_standard, mssql_custom]
#    enable_ping: true
#
#  - name: mssql_database_ecb
#    data_source_name: 'sqlserver://zabbix_reader:<EMAIL>/ecb?database=master'
#    collectors: [mssql_standard, mssql_custom]
#    enable_ping: true
#
#  - name: mssql_database_ecc
#    data_source_name: 'sqlserver://zabbix_reader:<EMAIL>/ecc?database=master'
#    collectors: [mssql_standard, mssql_custom]
#    enable_ping: true
#
#  - name: mssql_database_SHARED
#    data_source_name: 'sqlserver://zabbix_reader:<EMAIL>/SHARED?database=master'
#    collectors: [mssql_standard, mssql_custom]
#    enable_ping: true
#
#  - name: mssql_database_INTERNAL
#    data_source_name: 'sqlserver://zabbix_reader:<EMAIL>/INTERNAL?database=master'
#    collectors: [mssql_standard, mssql_custom]
#    enable_ping: true
#
# A collector is a named set of related metrics that are collected together. It can be referenced by name, possibly
# along with other collectors.
#
# Collectors may be defined inline (under `collectors`) or loaded from `collector_files` (one collector per file).
collectors:
  - collector_name: mssql_standard
    metrics:
      - metric_name: mssql_hostname
        type: gauge
        help: 'Database server hostname'
        key_labels:
          - hostname
        static_value: 1
        query: |
          SELECT @@SERVERNAME AS hostname

  - collector_name: mssql_custom
    metrics:
      - metric_name: mssql_job_status
        type: gauge
        help: 'Status of SQL agent jobs.'
        key_labels:
          - JobName
        values: [JobStatus]
        query: |
          SELECT 
            j.name AS JobName,
            CASE 
              WHEN h.run_status = 0 THEN 0
              WHEN h.run_status = 1 THEN 1
              WHEN h.run_status = 2 THEN 2
              WHEN h.run_status = 3 THEN 3
              WHEN h.run_status = 4 THEN 4
              ELSE -1
            END AS JobStatus
          FROM msdb.dbo.sysjobs AS j
          LEFT JOIN msdb.dbo.sysjobhistory AS h ON j.job_id = h.job_id
          WHERE h.instance_id = (
            SELECT MAX(instance_id)
            FROM msdb.dbo.sysjobhistory
            WHERE job_id = j.job_id
          )

      - metric_name: mssql_buffer_cache_hit_ratio
        type: gauge
        help: 'Buffer cache hit ratio.'
        values: [BufferCacheHitRatio]
        query: |
          SELECT 
            (CASE 
              WHEN d.cntr_value = 0 THEN 0 
              ELSE CAST(a.cntr_value AS FLOAT) / CAST(b.cntr_value AS FLOAT) * 100
            END) AS BufferCacheHitRatio
          FROM 
            sys.dm_os_performance_counters AS a
            JOIN sys.dm_os_performance_counters AS b ON a.object_name = b.object_name
            JOIN sys.dm_os_performance_counters AS d ON a.object_name = d.object_name
          WHERE 
            a.counter_name = 'Buffer cache hit ratio' 
            AND b.counter_name = 'Buffer cache hit ratio base' 
            AND d.counter_name = 'Page life expectancy'

      - metric_name: mssql_service_unavailable
        type: gauge
        help: 'The TCP port of the MSSQL Server service is currently unavailable.'
        values: [ServiceUnavailable]
        query: |
          SELECT 
            CASE 
              WHEN NOT EXISTS (
                SELECT 1
                FROM sys.dm_exec_connections
                WHERE local_net_address = 'localhost' AND local_tcp_port = 1433
              ) THEN 1 
              ELSE 0 
            END AS ServiceUnavailable

      - metric_name: mssql_service_restarted
        type: gauge
        help: 'Uptime is less than 10 minutes.'
        values: [ServiceRestarted]
        query: |
          SELECT 
            CASE 
              WHEN (SELECT sqlserver_start_time FROM sys.dm_os_sys_info) > DATEADD(MINUTE, -10, GETDATE()) THEN 1 
              ELSE 0 
            END AS ServiceRestarted

      - metric_name: mssql_failed_fetch_info
        type: gauge
        help: 'Zabbix has not received any data for items for the last 30 minutes.'
        values: [FailedFetchInfo]
        query: |
          SELECT 
            CASE 
              WHEN NOT EXISTS (
                SELECT 1
                FROM sys.dm_exec_requests
                WHERE start_time > DATEADD(MINUTE, -30, GETDATE())
              ) THEN 1 
              ELSE 0 
            END AS FailedFetchInfo

      - metric_name: mssql_low_buffer_cache_efficiency
        type: gauge
        help: 'Percentage of the buffer cache efficiency is low.'
        values: [LowBufferCacheEfficiency]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MAX(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Buffer cache hit ratio'
              ) < 30 THEN 1 
              ELSE 0 
            END AS LowBufferCacheEfficiency

      - metric_name: mssql_low_buffer_cache_efficiency_warning
        type: gauge
        help: 'Low buffer cache hit ratio.'
        values: [LowBufferCacheEfficiencyWarning]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MAX(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Buffer cache hit ratio'
              ) < 50 THEN 1 
              ELSE 0 
            END AS LowBufferCacheEfficiencyWarning

      - metric_name: mssql_high_lazy_writer_buffers
        type: gauge
        help: 'Number of buffers written per second by the lazy writer is high.'
        values: [HighLazyWriterBuffers]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Lazy writes/sec'
              ) > 20 THEN 1 
              ELSE 0 
            END AS HighLazyWriterBuffers

      - metric_name: mssql_low_page_life_expectancy
        type: gauge
        help: 'Page life expectancy is low.'
        values: [LowPageLifeExpectancy]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MAX(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Page life expectancy'
              ) < 300 THEN 1 
              ELSE 0 
            END AS LowPageLifeExpectancy

      - metric_name: mssql_high_physical_page_reads
        type: gauge
        help: 'Number of physical database page reads per second is high.'
        values: [HighPhysicalPageReads]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Page reads/sec'
              ) > 90 THEN 1 
              ELSE 0 
            END AS HighPhysicalPageReads

      - metric_name: mssql_high_physical_page_writes
        type: gauge
        help: 'Number of physical database page writes per second is high.'
        values: [HighPhysicalPageWrites]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Page writes/sec'
              ) > 90 THEN 1 
              ELSE 0 
            END AS HighPhysicalPageWrites

      - metric_name: mssql_too_many_physical_reads
        type: gauge
        help: 'Too many physical reads occurring.'
        values: [TooManyPhysicalReads]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT cntr_value
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Readahead pages/sec'
              ) > 20 / 100 * (
                SELECT cntr_value
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Page reads/sec'
              ) THEN 1 
              ELSE 0 
            END AS TooManyPhysicalReads

      - metric_name: mssql_high_average_wait_time
        type: gauge
        help: 'Total average wait time for locks is high.'
        values: [HighAverageWaitTime]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Average Wait Time (ms)'
              ) > 500 THEN 1 
              ELSE 0 
            END AS HighAverageWaitTime

      - metric_name: mssql_high_lock_requests
        type: gauge
        help: 'Total number of locks per second is high.'
        values: [HighLockRequests]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Lock Requests/sec'
              ) > 1000 THEN 1 
              ELSE 0 
            END AS HighLockRequests

      - metric_name: mssql_high_lock_timeouts
        type: gauge
        help: 'Total lock requests per second that timed out is high.'
        values: [HighLockTimeouts]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Lock Timeouts/sec'
              ) > 1 THEN 1 
              ELSE 0 
            END AS HighLockTimeouts

      - metric_name: mssql_some_blocking_occurring
        type: gauge
        help: 'Some blocking is occurring for 5m.'
        values: [SomeBlockingOccurring]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Lock Waits/sec'
              ) > 0 THEN 1 
              ELSE 0 
            END AS SomeBlockingOccurring

      - metric_name: mssql_high_number_of_deadlocks
        type: gauge
        help: 'Number of deadlocks is high.'
        values: [HighNumberOfDeadlocks]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Number of Deadlocks/sec'
              ) > 1 THEN 1 
              ELSE 0 
            END AS HighNumberOfDeadlocks

      - metric_name: mssql_high_index_table_scans
        type: gauge
        help: 'Number of index and table scans exceeds index searches in the last 15m.'
        values: [HighIndexTableScans]
        query: |
          SELECT 
            CASE 
              WHEN (
                SELECT MIN(cntr_value)
                FROM sys.dm_os_performance_counters
                WHERE counter_name = 'Full Scans/sec'
              ) > 0.001 THEN 1 
              ELSE 0 
            END AS HighIndexTableScans
