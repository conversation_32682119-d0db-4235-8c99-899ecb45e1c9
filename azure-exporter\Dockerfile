FROM python:3.11-slim

WORKDIR /app

# Install dependencies including curl for healthcheck
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY exporter.py .

# Expose metrics port
EXPOSE 9912

# Health check using python
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:9912/metrics')" || exit 1

# Run the exporter
CMD ["python", "exporter.py"]