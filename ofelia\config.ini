[job-local "uname"]
schedule = @hourly
command = "uname -a"

[job-exec "logrotate_librenms_traefik"]
schedule = @every 24h
command = sh -c 'find /var/log/traefik -name *.log -type f -exec rm -rfv {} + && kill -USR1 1'
container = traefik


[job-exec "logrotate_grafana"]
schedule = @every 1h
command = sh -c 'find /var/log/grafana -name "grafana.log.*" -type f -mtime +1 -exec rm -rfv {} + && kill -USR1 1'
container = grafana