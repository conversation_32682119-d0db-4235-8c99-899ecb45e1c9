{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 6, "x": 0, "y": 0}, "id": 6, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"def": {"name": "offset", "category": "Transform", "params": [{"name": "delta", "type": "float", "options": [-100, 100]}], "defaultParams": [100]}, "params": ["-48"], "text": "offset(-48)", "added": false, "$$hashKey": "object:62"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)", "$$hashKey": "object:59"}], "group": {"filter": "Sentia-cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Production Sentia - NGHW-clu1 -cluster HyperV Number of Virtual CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 6, "x": 6, "y": 0}, "id": 29, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"def": {"name": "offset", "category": "Transform", "params": [{"name": "delta", "type": "float", "options": [-100, 100]}], "defaultParams": [100]}, "params": ["-48"], "text": "offset(-48)", "added": false, "$$hashKey": "object:91"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)", "$$hashKey": "object:88"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Production Sentia - NGHW-clu2 -cluster HyperV Number of Virtual CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 6, "x": 12, "y": 0}, "id": 3, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"def": {"name": "offset", "category": "Transform", "params": [{"name": "delta", "type": "float", "options": [-100, 100]}], "defaultParams": [100]}, "params": ["-72"], "text": "offset(-72)", "$$hashKey": "object:175"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)", "$$hashKey": "object:176"}], "group": {"filter": "ITO-Cluster"}, "hide": false, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "ITO-Cluster HyperV Number of  Virtual CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"def": {"name": "offset", "category": "Transform", "params": [{"name": "delta", "type": "float", "options": [-100, 100]}], "defaultParams": [100]}, "params": ["-48"], "text": "offset(-48)", "$$hashKey": "object:204"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)", "$$hashKey": "object:205"}], "group": {"filter": "ITO-Cluster02"}, "hide": false, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "ITO-Cluster02  HyperV Number of Virtual CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "displayName": "<PERSON><PERSON>", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 12}, "id": 25, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:33", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:34", "def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["Logical "], "text": "<PERSON><PERSON><PERSON><PERSON>(Logical )"}], "group": {"filter": "Sentia-cluster"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:61", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:62", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/Virtual (.*)/", "$1"], "text": "replaceAlias(/Virtual (.*)/, $1)"}], "group": {"filter": "Sentia-cluster"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "($B-$A)/$A", "hide": false, "refId": "C", "type": "math"}], "title": "Production Sentia - NGHW-clu1-cluster RATIO", "transformations": [{"disabled": true, "id": "calculateField", "options": {"mode": "reduceRow", "reduce": {"include": [], "reducer": "sum"}}}], "type": "stat"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "displayName": "<PERSON><PERSON>", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 12}, "id": 30, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:96", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:97", "def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["Logical "], "text": "<PERSON><PERSON><PERSON><PERSON>(Logical )"}], "group": {"filter": "nghw-clu2"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:119", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:120", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/Virtual (.*)/", "$1"], "text": "replaceAlias(/Virtual (.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "($B-$A)/$A", "hide": false, "refId": "C", "type": "math"}], "title": "Production Sentia - NGHW-clu2-cluster RATIO", "transformations": [{"disabled": true, "id": "calculateField", "options": {"mode": "reduceRow", "reduce": {"include": [], "reducer": "sum"}}}], "type": "stat"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "displayName": "<PERSON><PERSON>", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 12}, "id": 24, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:208", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:229", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "($B-$A)/$A", "hide": false, "refId": "C", "type": "math"}], "title": "ITO-Cluster RATIO", "type": "stat"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "displayName": "<PERSON><PERSON>", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 12}, "id": 26, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:262", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster02"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:283", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster02"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "($B-$A)/$A", "hide": false, "refId": "C", "type": "math"}], "title": "ITO-Cluster02 RATIO", "type": "stat"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "displayName": "<PERSON><PERSON>", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 16}, "id": 16, "options": {"legend": {"calcs": ["lastNotNull", "min", "max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:123", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:124", "def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["Logical "], "text": "<PERSON><PERSON><PERSON><PERSON>(Logical )"}], "group": {"filter": "Sentia-cluster"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:146", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:147", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/Virtual (.*)/", "$1"], "text": "replaceAlias(/Virtual (.*)/, $1)"}], "group": {"filter": "Sentia-cluster"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "$B/$A", "hide": false, "refId": "C", "type": "math"}], "title": "Production Sentia - NGHW-clu1-cluster RATIO", "transformations": [{"disabled": true, "id": "calculateField", "options": {"mode": "reduceRow", "reduce": {"include": [], "reducer": "sum"}}}], "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "displayName": "<PERSON><PERSON>", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 16}, "id": 31, "options": {"legend": {"calcs": ["lastNotNull", "min", "max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:320", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:321", "def": {"category": "<PERSON><PERSON>", "defaultParams": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "alias", "type": "string"}]}, "params": ["Logical "], "text": "<PERSON><PERSON><PERSON><PERSON>(Logical )"}], "group": {"filter": "nghw-clu2"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:343", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:344", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/Virtual (.*)/", "$1"], "text": "replaceAlias(/Virtual (.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "$B/$A", "hide": false, "refId": "C", "type": "math"}], "title": "Production Sentia - NGHW-clu2-cluster RATIO", "transformations": [{"disabled": true, "id": "calculateField", "options": {"mode": "reduceRow", "reduce": {"include": [], "reducer": "sum"}}}], "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "displayName": "<PERSON><PERSON>", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 16}, "id": 13, "options": {"legend": {"calcs": ["lastNotNull", "min", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:150", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:171", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "$B/$A", "hide": false, "refId": "C", "type": "math"}], "title": "ITO-Cluster RATIO", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 16}, "id": 15, "options": {"legend": {"calcs": ["lastNotNull", "min", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:600", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster02"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:621", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster02"}, "hide": true, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": " $B/ $A", "hide": false, "refId": "C", "type": "math"}], "title": "ITO-Cluster02  RATIO", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 20}, "id": 22, "options": {"legend": {"calcs": ["lastNotNull", "min", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:175", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:176", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "Sentia-cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Production Sentia - NGHW-clu1-cluster - HyperV Number of Virtual CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 20}, "id": 32, "options": {"legend": {"calcs": ["lastNotNull", "min", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:372", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:373", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Prod Sentia - NGHW-clu2-cluster  HyperV Number of Virtual CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 20}, "id": 27, "options": {"legend": {"calcs": ["lastNotNull", "min", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:198", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:199", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "ITO-Cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "ITO-Cluster RATIO - HyperV Number of Virtual CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 20}, "id": 28, "options": {"legend": {"calcs": ["lastNotNull", "min", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:227", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:228", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "ITO-Cluster02"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "ITO-Cluster02 RATIO - HyperV Number of Virtual CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 0, "y": 23}, "id": 10, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:260", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "Sentia-cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Sentia HyperV Logical CPUs", "type": "gauge"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 3, "y": 23}, "id": 11, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:287", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "Sentia-cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "SentiaHyperV Virtual CPUs", "type": "gauge"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 6, "y": 23}, "id": 36, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:540", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Sentia HyperV Logical CPUs-CLU02", "type": "gauge"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 9, "y": 23}, "id": 37, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:567", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "SentiaHyperV Virtual CPUs- CLU02", "type": "gauge"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 12, "y": 23}, "id": 12, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:594", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "ITO-Cluster HyperV Logical CPUs", "type": "gauge"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 15, "y": 23}, "id": 17, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:621", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "ITO-Cluster HyperV Virtual CPUs", "type": "gauge"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 18, "y": 23}, "id": 14, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:648", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster02"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "ITO-Cluster02 HyperV Logical CPUs", "type": "gauge"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 21, "y": 23}, "id": 18, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.3.2", "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:675", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}], "group": {"filter": "ITO-Cluster02"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Virtual CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "ITO-Cluster02 HyperV Virtual CPUs", "type": "gauge"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 28}, "id": 23, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:759", "added": false, "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:756", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "Sentia-cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Sentia-cluster HyperV Number of Logical CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 28}, "id": 33, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:511", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:512", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Sentia-cluster CLu02 -HyperV Number of Logical CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 28}, "id": 35, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:729", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:730", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "ITO-Cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Sentia-cluster HyperV Number of Logical CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 28}, "id": 38, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:758", "def": {"category": "Aggregate", "defaultParams": [], "name": "sumSeries", "params": []}, "params": [], "text": "sumSeries()"}, {"$$hashKey": "object:759", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "ITO-Cluster02"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Sentia-cluster HyperV Number of Logical CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 6, "x": 0, "y": 33}, "id": 9, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:787", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "Sentia-cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Production Sentia - NGHW-clu1-cluster HyperV Number of Logical CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 12, "w": 6, "x": 6, "y": 33}, "id": 34, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:814", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "nghw-clu2"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "Production Sentia - NGHW-clu2 cluster HyperV Number of Logical CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 13, "w": 6, "x": 12, "y": 33}, "id": 2, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:702", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "ITO-Cluster"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "ITO-Cluster HyperV Number of Logical CPUs", "type": "timeseries"}, {"datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 3, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["hv-sa-09.: HyperV Number of Logical CPUs"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 13, "w": 6, "x": 18, "y": 33}, "id": 5, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"$$hashKey": "object:949", "added": false, "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/scalepoint.tech(.*)/", "$1"], "text": "replaceAlias(/scalepoint.tech(.*)/, $1)"}], "group": {"filter": "ITO-Cluster02"}, "host": {"filter": "/.*/"}, "item": {"filter": "HyperV Number of Logical CPUs"}, "itemTag": {"filter": ""}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "ITO-Cluster02 HyperV Number of Logical CPUs", "type": "timeseries"}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "CPU Virtual and logical", "uid": "bPjQZN_4z", "version": 12, "weekStart": ""}