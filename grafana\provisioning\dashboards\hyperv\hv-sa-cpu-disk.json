{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Filesystem C:"}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"def": {"name": "top", "category": "Filter", "params": [{"name": "number", "type": "int"}, {"name": "value", "type": "string", "options": ["avg", "min", "max", "sum", "count", "median", "first", "last"]}], "defaultParams": [5, "avg"]}, "params": [5, "avg"], "text": "top(5, avg)", "added": false, "$$hashKey": "object:1166"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: C:: Space utilization(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: C:: Space utilization(.*)/, $1)", "$$hashKey": "object:1083"}], "group": {"filter": "CPH-Hyper-v"}, "host": {"filter": "/.*hv-sa/"}, "item": {"filter": "C:: Space utilization"}, "itemTag": {"filter": "Application: Filesystem C:"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "C:: Space utilization", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "series", "show": true, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:247", "format": "percent", "label": "ddsa", "logBase": 1, "show": true}, {"$$hashKey": "object:248", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Filesystem D:"}, "functions": [{"$$hashKey": "object:64", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:65", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.tech: D:: Space utilization", ":"], "text": "replaceAlias(.scalepoint.tech: D:: Space utilization, :)"}], "group": {"filter": "CPH-Hyper-v"}, "host": {"filter": "/.*hv-sa/"}, "item": {"filter": "D:: Space utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "D:: Space utilization", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "series", "show": true, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:109", "format": "percent", "logBase": 1, "max": "100", "show": true}, {"$$hashKey": "object:110", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 8}, "hiddenSeries": false, "id": 18, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/hv-sa-01.scalepoint.tech:(.*)/", "$1"], "text": "replaceAlias(/hv-sa-01.scalepoint.tech:(.*)/, $1)", "$$hashKey": "object:968"}, {"def": {"name": "top", "category": "Filter", "params": [{"name": "number", "type": "int"}, {"name": "value", "type": "string", "options": ["avg", "min", "max", "sum", "count", "median", "first", "last"]}], "defaultParams": [5, "avg"]}, "params": [5, "avg"], "text": "top(5, avg)", "added": false, "$$hashKey": "object:1052"}, {"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": ["/Space utilization(.*)/", "$1"], "text": "replaceAlias(/Space utilization(.*)/, $1)", "$$hashKey": "object:967"}], "group": {"filter": "CPH-Hyper-v"}, "host": {"filter": "/.*hv-sa-01/"}, "item": {"filter": "/.*Space utilization/"}, "itemTag": {"filter": "Application: Filesystem C:"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "hv-sa-01 - Space utilization", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "series", "show": true, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:247", "format": "percent", "label": "ddsa", "logBase": 1, "show": true}, {"$$hashKey": "object:248", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 8}, "hiddenSeries": false, "id": 19, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "datasource": {"type": "alexanderzobnin-zabbix-datasource", "uid": "PCAF1C06DCF802728"}, "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/hv-sa-02.scalepoint.tech:(.*)/", "$1"], "text": "replaceAlias(/hv-sa-02.scalepoint.tech:(.*)/, $1)", "$$hashKey": "object:852"}, {"def": {"name": "top", "category": "Filter", "params": [{"name": "number", "type": "int"}, {"name": "value", "type": "string", "options": ["avg", "min", "max", "sum", "count", "median", "first", "last"]}], "defaultParams": [5, "avg"]}, "params": ["30", "avg"], "text": "top(30, avg)", "added": false, "$$hashKey": "object:936"}, {"def": {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}], "defaultParams": ["/(.*)/", "$1"]}, "params": ["/Space utilization(.*)/", "$1"], "text": "replaceAlias(/Space utilization(.*)/, $1)", "$$hashKey": "object:851"}], "group": {"filter": "CPH-Hyper-v"}, "host": {"filter": "/.*hv-sa-02/"}, "item": {"filter": "/.*Space utilization/"}, "itemTag": {"filter": "Application: Filesystem C:"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "hv-sa-02 - Space utilization", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "series", "show": true, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:247", "format": "percent", "label": "ddsa", "logBase": 1, "show": true}, {"$$hashKey": "object:248", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 5, "x": 0, "y": 19}, "hiddenSeries": false, "id": 13, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "HyperV Host"}, "functions": [{"$$hashKey": "object:318", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: HyperV VMs Critical(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: HyperV VMs Critical(.*)/, $1)"}, {"$$hashKey": "object:319", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}], "group": {"filter": "CPH-Hyper-v"}, "host": {"filter": "/.*hv-sa-/"}, "item": {"filter": "HyperV VMs Critical"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "HyperV VMs Critical", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "zabbix", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 9, "w": 7, "x": 5, "y": 19}, "id": 15, "links": [], "options": {"displayMode": "gradient", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}}, "pluginVersion": "8.5.2", "targets": [{"application": {"filter": "Status"}, "datasource": "zabbix", "functions": [{"$$hashKey": "object:52", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:53", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: Uptime(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: Uptime(.*)/, $1)"}], "group": {"filter": "CPH-Hyper-v"}, "host": {"filter": "/.*hv-sa-/"}, "item": {"filter": "Uptime"}, "itemTag": {"filter": "Application: Status"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": "0", "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "title": "System uptime", "type": "bargauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 19}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Memory"}, "functions": [{"$$hashKey": "object:158", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:159", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: Memory utilization(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: Memory utilization(.*)/, $1)"}], "group": {"filter": "CPH-Hyper-v"}, "hide": false, "host": {"filter": "/.*hv-sa-/"}, "item": {"filter": "Memory utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Memory utilization", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:359", "format": "percent", "logBase": 1, "show": true}, {"$$hashKey": "object:360", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "functions": [{"$$hashKey": "object:807", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.tech: Interface QLogic 2x25GE QL41262HMCU CNA ", "-"], "text": "replaceAlias(.scalepoint.tech: Interface QLogic 2x25GE QL41262HMCU CNA , -)"}, {"$$hashKey": "object:808", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "min"], "text": "top(30, min)"}], "group": {"filter": "CPH-Hyper-v"}, "hide": false, "host": {"filter": "/.*hv-sa/"}, "item": {"filter": "Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b): Bits sent"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "functions": [{"$$hashKey": "object:830", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.tech: Interface QLogic 2x25GE QL41262HMCU CNA", "-"], "text": "replaceAlias(.scalepoint.tech: Interface QLogic 2x25GE QL41262HMCU CNA, -)"}, {"$$hashKey": "object:831", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}], "group": {"filter": "CPH-Hyper-v"}, "hide": false, "host": {"filter": "/.*hv-sa/"}, "item": {"filter": "Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1a): Bits sent"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface QLogic 2x25GE QL41262HMCU  Bits sent", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:741", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:742", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {"unit": "bps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 36}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b)"}, "functions": [{"$$hashKey": "object:941", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.tech: Interface QLogic 2x25GE QL41262HMCU CNA ", "-"], "text": "replaceAlias(.scalepoint.tech: Interface QLogic 2x25GE QL41262HMCU CNA , -)"}, {"$$hashKey": "object:942", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}], "group": {"filter": "CPH-Hyper-v"}, "hide": false, "host": {"filter": "/.*hv-sa/"}, "item": {"filter": "Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1b): Bits received"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "B", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}, {"application": {"filter": ""}, "functions": [{"$$hashKey": "object:964", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": [".scalepoint.tech: Interface QLogic 2x25GE QL41262HMCU CNA", "-"], "text": "replaceAlias(.scalepoint.tech: Interface QLogic 2x25GE QL41262HMCU CNA, -)"}, {"$$hashKey": "object:965", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}], "group": {"filter": "CPH-Hyper-v"}, "hide": false, "host": {"filter": "/.*hv-sa/"}, "item": {"filter": "Interface QLogic 2x25GE QL41262HMCU CNA (NDIS)(Team1a): Bits received"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeRegions": [], "title": "Interface QLogic 2x25GE QL41262HMCU  Bits received", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:529", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:530", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": false, "schemaVersion": 36, "style": "dark", "tags": ["hosts", "zabbix"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "HV-SA"}