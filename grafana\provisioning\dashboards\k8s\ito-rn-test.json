{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "", "editable": true, "gnetId": null, "graphTooltip": 0, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": "CPU"}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)", "$$hashKey": "object:748"}, {"def": {"category": "Trends", "defaultParams": ["avg"], "name": "trendValue", "params": [{"name": "type", "options": ["avg", "min", "max", "sum", "count"], "type": "string"}]}, "params": ["count"], "text": "trendValue(count)", "$$hashKey": "object:749"}, {"def": {"category": "Transform", "defaultParams": [100], "name": "offset", "params": [{"name": "delta", "options": [-100, 100], "type": "float"}]}, "params": ["-100"], "text": "offset(-100)", "$$hashKey": "object:750"}, {"def": {"category": "Transform", "defaultParams": [100], "name": "scale", "params": [{"name": "factor", "options": [100, 0.01, 10, -1], "type": "float"}]}, "params": ["-1"], "text": "scale(-1)", "$$hashKey": "object:751"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: CPU idle time(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: CPU idle time(.*)/, $1)", "$$hashKey": "object:752"}], "group": {"filter": "K8s"}, "host": {"filter": "/.*ito-rn-test/"}, "intervalFactor": 1, "item": {"filter": "CPU idle time"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage - ito-rn-test", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:159", "format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:160", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "hiddenSeries": false, "id": 25, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: \\/: Space utilization(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: \\/: Space utilization(.*)/, $1)", "$$hashKey": "object:659"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/boot(.*)/", "$1"], "text": "replaceAlias(/boot(.*)/, $1)", "$$hashKey": "object:660"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["10", "avg"], "text": "top(10, avg)", "$$hashKey": "object:661"}], "group": {"filter": "K8s"}, "host": {"filter": "/.*ito-rn-test/"}, "intervalFactor": 1, "item": {"filter": "/: Space utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Space utilization on ito-rn-test-  /:", "tooltip": {"shared": true, "sort": 1, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1257", "format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1258", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": true, "alignLevel": null}}, {"datasource": "zabbix", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 4, "x": 0, "y": 16}, "id": 3, "links": [], "options": {"displayMode": "gradient", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {"titleSize": 12, "valueSize": 12}}, "pluginVersion": "7.5.3", "targets": [{"application": {"filter": "Status"}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:1265", "def": {"category": "Special", "defaultParams": ["avg"], "name": "consolidateBy", "params": [{"name": "type", "options": ["avg", "min", "max", "sum", "count"], "type": "string"}]}, "params": ["count"], "text": "consolidateBy(count)"}, {"$$hashKey": "object:1266", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:1267", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: System uptime(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: System uptime(.*)/, $1)"}], "group": {"filter": "K8s"}, "host": {"filter": "/.*ito-rn-test/"}, "intervalFactor": 1, "item": {"filter": "System uptime"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "timeFrom": null, "timeShift": null, "title": "System uptime ito-rn-test", "type": "bargauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 4, "y": 16}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: \\/var\\/lib\\/docker: Space utilization(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: \\/var\\/lib\\/docker: Space utilization(.*)/, $1)", "$$hashKey": "object:841"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/boot(.*)/", "$1"], "text": "replaceAlias(/boot(.*)/, $1)", "$$hashKey": "object:842"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["10", "max"], "text": "top(10, max)", "$$hashKey": "object:843"}], "group": {"filter": "K8s"}, "host": {"filter": "/.*ito-rn-test/"}, "intervalFactor": 1, "item": {"filter": "/var/lib/docker: Space utilization"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Space utilization ito-rn-test  /var/lib/docker", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1257", "format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1258", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 16}, "hiddenSeries": false, "id": 29, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "", "format": "time_series", "functions": [{"$$hashKey": "object:1177", "def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["30", "avg"], "text": "top(30, avg)"}, {"$$hashKey": "object:1178", "def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: Number of processes(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: Number of processes(.*)/, $1)"}], "group": {"filter": "K8s"}, "host": {"filter": "/.*ito-rn-test/"}, "intervalFactor": 1, "item": {"filter": "Number of processes"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [{"$$hashKey": "object:1063", "colorMode": "custom", "fill": false, "line": true, "lineColor": "rgb(255, 0, 0)", "op": "gt", "source": "zabbix", "value": 1200, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Number of processes - ito-rn-test", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1035", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1036", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 25}, "hiddenSeries": false, "id": 31, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: \\/var\\/lib\\/docker: Used space(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: \\/var\\/lib\\/docker: Used space(.*)/, $1)", "$$hashKey": "object:270"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/boot(.*)/", "$1"], "text": "replaceAlias(/boot(.*)/, $1)", "$$hashKey": "object:271"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["10", "max"], "text": "top(10, max)", "$$hashKey": "object:272"}], "group": {"filter": "K8s"}, "host": {"filter": "/.*ito-rn-test/"}, "intervalFactor": 1, "item": {"filter": "/var/lib/docker: Used space"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Used space  ito-rn-test  /var/lib/docker", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "series", "name": null, "show": true, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:1257", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1258", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "zabbix", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 25}, "hiddenSeries": false, "id": 32, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "", "format": "time_series", "functions": [{"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/.scalepoint.tech: \\/var\\/lib\\/docker: Total space(.*)/", "$1"], "text": "replaceAlias(/.scalepoint.tech: \\/var\\/lib\\/docker: Total space(.*)/, $1)", "$$hashKey": "object:359"}, {"def": {"category": "<PERSON><PERSON>", "defaultParams": ["/(.*)/", "$1"], "name": "<PERSON><PERSON><PERSON><PERSON>", "params": [{"name": "regexp", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}]}, "params": ["/boot(.*)/", "$1"], "text": "replaceAlias(/boot(.*)/, $1)", "$$hashKey": "object:360"}, {"def": {"category": "Filter", "defaultParams": [5, "avg"], "name": "top", "params": [{"name": "number", "type": "int"}, {"name": "value", "options": ["avg", "min", "max", "sum", "count", "median"], "type": "string"}]}, "params": ["15", "max"], "text": "top(15, max)", "$$hashKey": "object:361"}], "group": {"filter": "K8s"}, "host": {"filter": "/.*ito-rn-test/"}, "intervalFactor": 1, "item": {"filter": "/var/lib/docker: Total space"}, "options": {"disableDataAlignment": false, "showDisabledItems": false, "skipEmptyValues": false, "useZabbixValueMapping": false}, "proxy": {"filter": ""}, "queryType": 0, "refId": "A", "resultFormat": "time_series", "table": {"skipEmptyValues": false}, "tags": {"filter": ""}, "trigger": {"filter": ""}, "triggers": {"acknowledged": 2, "count": true, "minSeverity": 3}}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total space - ito-rn-test - /var/lib/docker", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "series", "name": null, "show": true, "values": ["current"]}, "yaxes": [{"$$hashKey": "object:1257", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1258", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["ito", "test", "Kubernetes", "k8s"], "templating": {"list": []}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "K8S ito-rn-test.scalepoint.tech"}