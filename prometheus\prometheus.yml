# 2021202
global:
  scrape_interval: 30s
  scrape_timeout: 20s
  evaluation_interval: 15s
rule_files:
  - 'alert-generic.rules'
  - 'alert-k8s.rules'
  - 'alert-haproxy.rules'
 # - 'alert-azure.rules' # certyficates expired
  #- 'alert-foreman-exporter.rules'
alerting:
  alertmanagers:
  - scheme: http
    static_configs:
    - targets: ['alertmanager:9093']
scrape_configs:
  - job_name: 'sql_exporter'
    static_configs:
      - targets: ['sql_exporter:9399']
        labels:
          app: "sql_exporter"
          team: "ito"

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
        labels:
          app: "prometheus"
          env: "int"
          team: "ito"
 # - job_name: 'pushgateway'
 #   honor_labels: true
 #   static_configs:
 #     - targets: ['pushgateway:9091']
 #       labels:
 #         app: "pushgateway"
 #         env: "int"
 #         team: "ito"
  - job_name: 'traefik'
    static_configs:
      - targets:
          - traefik:8083
          - jira-test.scalepoint.io:8082
        labels:
          app: "traefik"
          env: "int"
          team: "ito"
    basic_auth:
      username: admin
      password: pain1vote
  - job_name: 'consul'
    honor_labels: true
    consul_sd_configs:
    - server: 'consul:8500'
      datacenter: 'oebro'
    - refresh_interval: 30s
    - datacenter: "dc1"
    params:
      format: [prometheus]
    metrics_path: '/metrics'
    #metrics_path: '/v1/agent/metrics'
    relabel_configs:
    # Ignore target services unless they are tagged in Consul with 'metrics'.
    - source_labels: ['__meta_consul_service']
      regex:         '(.*)'
      target_label:  'job'
      replacement:   '$1'
    - source_labels: [__meta_consul_tags]
      regex: '.*,(metrics|prometheus),.*'
      action: keep
    - source_labels: [__meta_consul_tags]
      regex: ',(?:[^,]+,){0}([^=]+)=([^,]+),.*'
      replacement: '${2}'
      target_label: '${1}'
    - source_labels: [__meta_consul_tags]
      regex: ',(?:[^,]+,){1}([^=]+)=([^,]+),.*'
      replacement: '${2}'
      target_label: '${1}'
    - source_labels: [__meta_consul_tags]
      regex: ',(?:[^,]+,){2}([^=]+)=([^,]+),.*'
      replacement: '${2}'
      target_label: '${1}'
    - source_labels: [__meta_consul_tags]
      regex: ',(?:[^,]+,){3}([^=]+)=([^,]+),.*'
      replacement: '${2}'
      target_label: '${1}'
    - source_labels: [__meta_consul_tags]
      regex: ',(?:[^,]+,){4}([^=]+)=([^,]+),.*'
      replacement: '${2}'
      target_label: '${1}'
    - source_labels: [__meta_consul_tags]
      regex: ',(?:[^,]+,){5}([^=]+)=([^,]+),.*'
      replacement: '${2}'
      target_label: '${1}'
    - source_labels: [__meta_consul_tags]
      regex: ',(?:[^,]+,){6}([^=]+)=([^,]+),.*'
      replacement: '${2}'
      target_label: '${1}'
  - job_name: 'node-exporter'
    scrape_interval: 60s
    static_configs:
      - targets: ['node-exporter:9100']
  - job_name: 'prod-haproxy'
    scrape_interval: 60s
    static_configs:
      - targets: ['prod-haproxy03:9101','prod-haproxy04:9101', 'prod-haproxy05:9101', 'prod-haproxy06:9101']
        labels:
          app: "haproxy"
          env: "prod"
          team: "ito"
  - job_name: 'int-haproxy'
    scrape_interval: 60s
    static_configs:
      - targets: ['int-haproxy03:9101', 'int-haproxy04:9101']
        labels:
          app: "haproxy"
          env: "dev"
          team: "ito"
  - job_name: 'azure-certificates'
    static_configs:
      - targets: ['azure-secrets-exporter:9912']
    scrape_interval: 120s

  # test of elasticsearch
  - job_name: 'ito-graylog'
    scrape_interval: 60s
    static_configs:
      - targets: ['ito-graylog:9114']
        labels:
          app: "elasticsearch"
          env: "prod"
          team: "ito"
  - job_name: 'dev-graylog'
    scrape_interval: 120s
    static_configs:
      - targets: ['dev-graylog:9114']
        labels:
          app: "elasticsearch"
          env: "dev"
          team: "ito"
  - job_name: 'prod-graylog'
    scrape_interval: 60s
    static_configs:
      - targets: ['prod-graylog:9114']
        labels:
          app: "elasticsearch"
          env: "prod"
          team: "ito"
  - job_name: 'pjt-ecb-el01'
    scrape_interval: 120s
    static_configs:
      - targets: ['pjt-ecb-el01:9114']
        labels:
          app: "elasticsearch"
          env: "pjt"
          team: "ecb"
  - job_name: 'test-ecb-el-c1'
    scrape_interval: 120s
    static_configs:
      - targets: ['test-ecb-el-c1:9114']
        labels:
          app: "elasticsearch"
          env: "test"
          team: "ecb"
  - job_name: 'prod-ecb-el-c1'
    scrape_interval: 60s
    static_configs:
      - targets: ['prod-ecb-el-c1:9114']
        labels:
          app: "elasticsearch"
          env: "prod"
          team: "ecb"
  - job_name: redis_exporter
    scrape_interval: 60s
    static_configs:
      - targets: ['easyclaims-sandbox-redis:9121']
        labels:
          app: "redis"
          env: "dev"
          team: "ito"
      - targets: ['easyclaims-prod-redis:9121']
        labels:
          app: "redis"
          env: "prod"
          team: "ito"
  - job_name: 'prod-bigip'
    scrape_interval: 60s
    static_configs:
      - targets: ['bigip1:9143','bigip2:9144','bigip3:9145']
        labels:
          app: "bigip"
          env: "prod"
          team: "ito"
# Graylogs
  - job_name: 'gr-ito-graylog'
    scrape_interval: 120s
    metrics_path: '/api/plugins/org.graylog.plugins.metrics.prometheus/metrics'
    basic_auth:
      username: reader
      password: reader
    # Optional TLS configuration; see https://prometheus.io/docs/operating/configuration/#<tls_config>
    #tls_config:
    scheme: https
    tls_config:
      insecure_skip_verify: true
    static_configs:
      - targets: ['ito-graylog01.spcph.local:9000','ito-graylog02.spcph.local:9000','ito-graylog03.spcph.local:9000', 'ito-graylog04.spcph.local:9000']
        labels:
          app: "itograylog"
          env: "prod"
          team: "ito"
  - job_name: 'gr-dev-graylog'
    scrape_interval: 300s
    metrics_path: '/api/plugins/org.graylog.plugins.metrics.prometheus/metrics'
    basic_auth:
      username: <EMAIL>
      password: improvement#pleasant8
    # Optional TLS configuration; see https://prometheus.io/docs/operating/configuration/#<tls_config>
    #tls_config:
    scheme: https
    tls_config:
      insecure_skip_verify: true
    static_configs:
      - targets: ['dev-graylog1.spcph.local:9000','dev-graylog2.spcph.local:9000','dev-graylog3.spcph.local:9000']
        labels:
          app: "graylog"
          env: "dev"
          team: "ito"
  - job_name: 'gr-prod-graylog'
    scrape_interval: 120s
    scrape_timeout: 60s
    metrics_path: '/api/plugins/org.graylog.plugins.metrics.prometheus/metrics'
    basic_auth:
      username: <EMAIL>
      password: improvement#pleasant8
    # Optional TLS configuration; see https://prometheus.io/docs/operating/configuration/#<tls_config>
    #tls_config:
    scheme: https
    tls_config:
      insecure_skip_verify: true
    static_configs:
      - targets: ['prod-graylog1.scalepoint.lan:9000','prod-graylog2.scalepoint.lan:9000','prod-graylog3.scalepoint.lan:9000','prod-graylog4.scalepoint.lan:9000', 'prod-graylog5.scalepoint.lan:9000']
        labels:
          app: "graylog"
          env: "prod"
          team: "ito"
# Blackbox
  - job_name: 'blackbox_ito'
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [http_2xx_tls]  # Look for a HTTP 200 response.
    static_configs:
      - targets:
        - https://int-zabbix.scalepoint.tech
        - https://grafana.scalepoint.tech
        - https://jira.scalepoint.com
        - https://confluence.scalepoint.com
        - https://ipam.scalepoint.tech
        - https://int-grafana.scalepoint.tech
        - https://int-prometheus.scalepoint.tech
        - https://int-prometheus.scalepoint.tech/alertmanager
        - https://syn-taa.scalepoint.tech:5001
        - https://syn-pg.scalepoint.tech:5001
        labels:
          env: "ito"
          app: "website"
          team: "ito"
          tls: "true"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement:  blackbox_exporter:9115
  - job_name: 'blackbox_tls_cert_expiry'
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [http_2xx]  #  Checking TLS certificate validity, accepts all HTTP codes, trusts local CAs
    static_configs:
      - targets:
          - https://ito-proget.spcph.local
          - https://foreman.scalepoint.tech/users/login
          - https://dev-graylog.spcph.local
          - https://jira.scalepoint.com
          - https://ito-graylog.spcph.local
          - https://ito-git.spcph.local
          - https://bamboo.spcph.local
          - https://deploy.scalepoint.lan
          - https://prod-shr-av02.scalepoint.lan:8080/spe/api/v1/service-status
          - https://dev-hvst-rn.scalepoint.tech/
          - https://vpn2.scalepoint.com
          - https://vpn1.scalepoint.com
          - https://prod-graylog.scalepoint.lan
          - https://claim-brewery-production.scalepoint.work/healthcheck/ready
          - https://easy-offer.scalepoint.work/
          - https://edi-regres-service-production.scalepoint.work/healthcheck
          - https://entry.scalepoint.com/easy-admin
          - https://entry.scalepoint.com/easy-start/healthcheck/live
          - https://myclaim.scalepoint.com
          - https://postalcode-prod.scalepoint.work/postalcode/actuator/health
          - https://prod-ecx-finance.scalepoint.work/healthcheck/ready
          - https://prod-mailservice.scalepoint.work
          - https://prod-process-director.scalepoint.work/
          - https://prod-shop-evb.scalepoint.work/healthcheck/live
          - https://prod-shr-pdf.scalepoint.work/healthcheck/ready
          - https://vm-ecc.scalepoint.work

          - https://entry.scalepoint.com/ecx/codan/DistributedApplicationHealthcheck
          - https://scalepoint.dk
          - https://scalepoint.com
          - https://validationportal.scalepoint.eu
          - https://fg-ems.scalepoint.tech
          - https://fg-analyzer.scalepoint.tech
          - https://fortimanager.scalepoint.tech/ui/login
          - https://oob01-ald.scalepoint.tech
          - https://oob02-pro.scalepoint.tech
          - https://dpanalyzer.scalepoint.tech
          - https://dev-hvst-rn.scalepoint.tech
          - https://syn-taa.scalepoint.tech:5001
          - https://syn-pg.scalepoint.tech:5001
          - https://ito-veeamone.scalepoint.tech:1239
          - https://dpanalyzer.scalepoint.tech
        labels:
          env: "ito"
          app: "website"
          team: "ito"
          tls: "true"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox_exporter:9115
  - job_name: 'blackbox_ito_nontls'
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [http_2xx]  # Look for a HTTP 200 response.
    static_configs:
      - targets:
        - https://ito-proget.spcph.local
        - https://foreman.scalepoint.tech/users/login
        - https://dev-graylog.spcph.local
        - https://ito-graylog.spcph.local
        - https://ito-git.spcph.local
        - https://bamboo.spcph.local
        - https://deploy.scalepoint.lan
        - https://prod-shr-av02.scalepoint.lan:8080/spe/api/v1/service-status
        labels:
          env: "ito"
          app: "website"
          team: "ito"
          tls: "false"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement:  blackbox_exporter:9115

  - job_name: 'blackbox_k8s'
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [http_2xx]  # Look for a HTTP 200 response.
    static_configs:
      - targets:
        - https://dev-hvst-rn.scalepoint.tech/
        labels:
          env: "ito"
          app: "k8s"
          team: "ito"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement:  blackbox_exporter:9115
  - job_name: 'blackbox_vpn'
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [http_vpn]  # Look for a HTTP 200 response.
    static_configs:
      - targets:
        - https://vpn2.scalepoint.com
        - https://vpn1.scalepoint.com
        labels:
          env: "ito"
          app: "vpn"
          team: "ito"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement:  blackbox_exporter:9115
  - job_name: 'blackbox_sftp'
    metrics_path: /probe
    params:
      module: [ssh_banner]
    static_configs:
      - targets:
        - filetransfer.scalepoint.com
        labels:
          env: "ito"
          app: "sftp"
          team: "ito"
    relabel_configs:
      # Ensure port is 22, pass as URL parameter
      - source_labels: [__address__]
        regex: (.*?)(:.*)?
        replacement: ${1}:22
        target_label: __param_target
      # Make instance label the target
      - source_labels: [__param_target]
        target_label: instance
      # Actually talk to the blackbox exporter though
      - target_label: __address__
        replacement: blackbox_exporter:9115
  - job_name: 'blackbox_prod'
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://prod-graylog.scalepoint.lan
      #  - http://prod-shr-mq.scalepoint.work
        labels:
          env: "prod"
          app: "website"
          team: "ito"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement:  blackbox_exporter:9115
  - job_name: 'blackbox_prod_kubernetes'
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://claim-brewery-production.scalepoint.work/healthcheck/ready
        - https://easy-offer.scalepoint.work/
        - https://edi-regres-service-production.scalepoint.work/healthcheck
        - https://entry.scalepoint.com/easy-admin
        - https://entry.scalepoint.com/easy-start/healthcheck/live
        - https://myclaim.scalepoint.com
        - https://postalcode-prod.scalepoint.work/postalcode/actuator/health
        - https://prod-ecx-finance.scalepoint.work/healthcheck/ready
        - https://prod-mailservice.scalepoint.work
        - https://prod-process-director.scalepoint.work/
        - https://prod-shop-evb.scalepoint.work/healthcheck/live
        - https://prod-shr-pdf.scalepoint.work/healthcheck/ready
        - https://validationportal.scalepoint.eu
        - https://vm-ecc.scalepoint.work
        labels:
          env: "prod"
          app: "kubernetes"
          team: "ito"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement:  blackbox_exporter:9115
  - job_name: 'blackbox_prod_kubernetes_redirect'
    scrape_interval: 30s
    metrics_path: /probe
    params:
      module: [http_auth_check]  # For endpoints requiring authentication
    static_configs:
      - targets:
        - https://fileservice-prod.scalepoint.work/api/fs/actuator/ping
        labels:
          env: "prod"
          app: "kubernetes"
          team: "ito"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement:  blackbox_exporter:9115
     # - source_labels: [__meta_blackbox_module]
     #   regex: http_2xx
     #   replacement: http_3xx
  - job_name: 'blackbox_prod_overall'
    metrics_path: /probe
    params:
      module: [http_overall_ok]
    static_configs:
      - targets:
        - https://entry.scalepoint.com/ecx/codan/DistributedApplicationHealthcheck
        labels:
          env: "prod"
          app: "website"
          team: "ecx"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox_exporter:9115
  - job_name: 'blackbox_ito_overall'
    metrics_path: /probe
    params:
      module: [http_overall_ok]
    static_configs:
      - targets:
        - https://scalepoint.dk
        - https://scalepoint.com
        - https://validationportal.scalepoint.eu
        - https://fg-ems.scalepoint.tech
        - https://fg-analyzer.scalepoint.tech
        - https://fortimanager.scalepoint.tech/
        - https://oob01-ald.scalepoint.tech
        - https://oob02-pro.scalepoint.tech
        - https://fs01-ald.scalepoint.tech
        - https://fs02-ald.scalepoint.tech
        - https://fs03-ald.scalepoint.tech
        - https://fs04-ald.scalepoint.tech
        - https://fs05-ald.scalepoint.tech
        - https://fs06-ald.scalepoint.tech
        - https://fs07-ald.scalepoint.tech
        - https://fs08-ald.scalepoint.tech
        - https://dpanalyzer.scalepoint.tech
        labels:
          env: "prod"
          app: "website"
          team: "ito"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox_exporter:9115
  - job_name: 'blackbox_dns'
    metrics_path: /probe
    scrape_interval: 60s
    static_configs:
      - targets:
        - dns_smtp_spcph_local:prod-dc01.scalepoint.lan
        - dns_smtp_spcph_local:prod-dc04.scalepoint.lan
        - dns_smtp_spcph_local:corp-dc01.spcph.local
        - dns_smtp_spcph_local:corp-dc02.spcph.local
        - dns_smtp_spcph_local:corp-dc03.spcph.local
        - dns_scalepoint_tech:tech-dc03.scalepoint.tech
        - dns_scalepoint_tech:tech-dc04.scalepoint.tech
        - dns_scalepoint_io:corp-dc01.spcph.local
        - dns_scalepoint_io:corp-dc02.spcph.local
        - dns_scalepoint_io:corp-dc03.spcph.local
        - dns_scalepoint_eu:*******
        - dns_scalepoint_eu:*******
        - dns_scalepoint_dk:*******
        - dns_scalepoint_dk:*******
        labels:
          env: "prod"
          app: "dns"
          team: "ito"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - source_labels: [__param_target]
        regex: '(.*):.*$'
        replacement: '$1'
        target_label: 'module'
      - source_labels: [__param_target]
        regex: '.*:(.*)$'
        replacement: '$1'
        target_label: 'instance'
      - source_labels: [__param_target]
        regex: '(.*):.*$'
        replacement: '$1'
        target_label: __param_module
      - source_labels: [__param_target]
        regex: '.*:(.*)$'
        replacement: '$1'
        target_label: __param_target
      - target_label: __address__
        replacement: blackbox_exporter:9115
  - job_name: 'foreman_exporter'
    scrape_interval: 30s
    scrape_timeout: 30s
    honor_labels: true
    metrics_path: '/metrics'
    scheme: http
    static_configs:
      - targets:
        - 'foreman_exporter:8000'
        labels:
          app: "foreman-exporter"
          env: "int"
          team: "ito"
  - job_name: 'federate_scalepoint-prod'
    scrape_interval: 120s
    scrape_timeout: 60s
    honor_labels: true
    metrics_path: '/federate'
    params:
      'match[]':
        - '{service=~"(expose-kubelets-metrics|expose-kubernetes-metrics|rancher-monitoring-kube|pushprox-kube|rancher-monitoring-ingress|rancher-monitoring-core|rancher-monitoring-prometheus-node-|cert-manager|openebs-).*"}'
        - '{__name__=~"job:.*"}'
    scheme: https
    tls_config:
      insecure_skip_verify: false
    static_configs:
      - targets:
        - 'prometheus.scalepoint-prod.scalepoint.tech'
        labels:
          cluster: scalepoint-prod
          cluster_env: prod
#  - job_name: 'federate_scalepoint-dev'
#    scrape_interval: 240s
#    scrape_timeout: 60s
#    honor_labels: true
#    metrics_path: '/federate'
#    params:
#      'match[]':
#        - '{service=~"(expose-kubelets-metrics|expose-kubernetes-metrics|rancher-monitoring-kube|pushprox-kube|rancher-monitoring-ingress|rancher-monitoring-core|rancher-monitoring-prometheus-node-|cert-manager|openebs-).*"}'
#        - '{__name__=~"job:.*"}'
#    scheme: https
#    tls_config:
#      insecure_skip_verify: false
#    static_configs:
#      - targets:
#        - 'scalepoint-dev-prometheus.scalepoint.dev'
#        labels:
#          cluster: scalepoint-dev
#          cluster_env: dev
#      - targets:
#        - 'dev-rn-p.scalepoint.tech'
#        labels:
#          cluster: dev-rn
#          cluster_env: dev
  - job_name: 'federate_custom_labels'
    scrape_interval: 60s
    scrape_timeout: 30s
    honor_labels: true
    metrics_path: '/federate'
    params:
      'match[]':
        - '{scalepointFederation="true"}'
    scheme: https
    tls_config:
      insecure_skip_verify: true
    static_configs:
      - targets:
        - 'prometheus.scalepoint-prod.scalepoint.tech'
        labels:
          cluster: scalepoint-prod
          cluster_env: prod
  - job_name: "windows_exporter_hyperv"
    scrape_interval: 60s
    scheme: http
    static_configs:
      - targets:
          - hv-sa-03.scalepoint.tech:9182
          - hv-sa-04.scalepoint.tech:9182
          - hv-sa-05.scalepoint.tech:9182
          - hv-sa-12.scalepoint.tech:9182
          - hv-sa-13.scalepoint.tech:9182
          - hv-sa-14.scalepoint.tech:9182
          - hv-sa-15.scalepoint.tech:9182
        labels:
          environment: dev
          location: progressive
          cluster: pghw-clu01
      - targets:
          - hv-sa-06.scalepoint.tech:9182
          - hv-sa-07.scalepoint.tech:9182
          - hv-sa-08.scalepoint.tech:9182
          - hv-sa-09.scalepoint.tech:9182
        labels:
          environment: dev
          location: progressive
          cluster: pghw-clu02
      - targets:
          - hv-sa-01.scalepoint.tech:9182
          - hv-sa-02.scalepoint.tech:9182
          - hpv-repl02.scalepoint.tech:9182
        labels:
          environment: unknown
          location: progressive
          cluster: unknown
      - targets:
          - nghw-6028.scalepoint.tech:9182
          - nghw-6029.scalepoint.tech:9182
          - nghw-6030.scalepoint.tech:9182
          - nghw-6031.scalepoint.tech:9182
          - nghw-6032.scalepoint.tech:9182
          - nghw-6033.scalepoint.tech:9182
          - nghw-6034.scalepoint.tech:9182
          - nghw-6035.scalepoint.tech:9182
          - nghw-9dm07v3.scalepoint.tech:9182
          - nghw-6192.scalepoint.tech:9182
        labels:
          environment: prod
          location: sentia
          cluster: nghw-clu01
      - targets:
          - nghw-1WPLLY3.scalepoint.tech:9182
          - nghw-2WPLLY3.scalepoint.tech:9182
          - nghw-3WPLLY3.scalepoint.tech:9182
          - nghw-4WPLLY3.scalepoint.tech:9182
        labels:
          environment: prod
          location: sentia
          cluster: nghw-clu02
    relabel_configs:
      - source_labels: [__address__]
        regex: '([^:]+):\d+'
        target_label: instance
  - job_name: "windows_exporter_mssql"
    scrape_interval: 60s
    scheme: http
    static_configs:
      - targets:
          - tst-sql-pro-01.scalepoint.lan:9182
        labels:
          environment: dev
          location: progressive
