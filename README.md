# Prometheus with docker-compose

## Components

* Prometheus
* AlertManager
* Traefik
* Grafana
* prometheus exporters
  * haproxy
  * elasticsearch
  * graylog
  * redis
  * F5 big-ip

## Usage

Push master branch to ito-git.spcph.local

### Data removal

```bash
curl -X POST -g 'https://int-prometheus.scalepoint.tech/api/v1/admin/tsdb/delete_series?match[]={job="blackbox_ito"}'
```

### Consul workaround

```bash
sudo chmod 777 -R /var/lib/docker/volumes/prometheus_consul_data
```

### Federation test

```bash
clear;curl -Gs --data-urlencode 'match[]={job=~".+"}' https://int-prometheus.scalepoint.tech/federate|grep -i kube
```

### Grafana image and repository

[https://github.com/marcinbojko/grafana-custom](https://github.com/marcinbojko/grafana-custom)

